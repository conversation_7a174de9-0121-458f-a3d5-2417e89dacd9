<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <SpaceMainSearch @search="reload" />
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        sticky
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        highlight-current-row
        :default-expand-all="true"
        :pagination="false"
        cache-key="infoSpaceMainTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            class="ele-btn-icon"
            :icon="ColumnHeightOutlined"
            @click="expandAll"
          >
            展开全部
          </el-button>
          <el-button
            class="ele-btn-icon"
            :icon="VerticalAlignMiddleOutlined"
            @click="foldAll"
          >
            折叠全部
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.INFO_COUNTRY_AREA_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <div style="display: inline-flex; align-items: center">
            <el-link
              type="primary"
              :underline="false"
              @click="openEdit(null, row.id)"
            >
              添加
            </el-link>
            <el-divider direction="vertical" style="margin: 0 8px" />
            <el-link type="primary" :underline="false" @click="openEdit(row)">
              修改
            </el-link>
            <template v-if="row.status === 0">
              <el-divider direction="vertical" style="margin: 0 8px" />
              <el-link
                type="primary"
                :underline="false"
                @click="enableSpace(row)"
              >
                启用
              </el-link>
              <el-divider direction="vertical" style="margin: 0 8px" />
              <el-link
                type="primary"
                :underline="false"
                @click="enableSpaceAll(row)"
              >
                全部启用
              </el-link>
            </template>
            <template v-else-if="row.status === 1">
              <el-divider direction="vertical" style="margin: 0 8px" />
              <el-link
                type="primary"
                :underline="false"
                @click="disableSpace(row)"
              >
                禁用
              </el-link>
            </template>
            <!-- 只有非顶级节点才显示删除按钮 -->
            <template v-if="row.parentId && row.parentId !== 0">
              <el-divider direction="vertical" style="margin: 0 8px" />
              <el-link type="danger" :underline="false" @click="remove(row)">
                删除
              </el-link>
            </template>
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <SpaceMainEdit
      v-model="showEdit"
      :data="current"
      :parent-id="parentId"
      @done="reload"
    />
  </ele-page>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage, toTree } from 'ele-admin-plus/es';
  import {
    PlusOutlined,
    ColumnHeightOutlined,
    VerticalAlignMiddleOutlined
  } from '@/components/icons';
  import SpaceMainSearch from './spacemain-search.vue';
  import SpaceMainEdit from './spacemain-edit.vue';
  import {
    deleteSpaceMain,
    getSpaceMainList,
    enabledSpaceMain,
    disabledSpaceMain
  } from '@/api/info/spacemain';
  import { DICT_TYPE } from '@/utils/dict';
  import { dateFormatter, dateFormatter2 } from '@/utils/formatTime';

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'spaceCode',
      label: '空间编码',
      minWidth: 100
    },
    {
      prop: 'spaceName',
      label: '空间名称',
      minWidth: 160
    },
    {
      prop: 'parkName',
      label: '所属园区',
      minWidth: 120
    },
    {
      prop: 'proviceCityArea',
      label: '省市区',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'addressDetail',
      label: '详细地址',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'floorCount',
      label: '楼层数',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'buildingHeight',
      label: '楼高(m)',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'totalArea',
      label: '建筑面积(㎡)',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'certificateArea',
      label: '产证面积(㎡)',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'rentalArea',
      label: '出租面积(㎡)',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'completionDate',
      label: '竣工日期',
      align: 'center',
      minWidth: 110,
      formatter: dateFormatter2
    },
    {
      prop: 'purpose',
      label: '用途',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      slot: 'status',
      minWidth: 100
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 110,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 250,
      align: 'center',
      slot: 'action',
      hideInPrint: true,
      fixed: 'right' // 固定操作列在右侧
    }
  ]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 上级空间id */
  const parentId = ref();

  /** 查询参数 */
  const queryParams = ref({
    spaceCode: '',
    spaceName: '',
    parkName: ''
  });

  /** 表格数据源 */
  const datasource = async ({ where }) => {
    const data = await getSpaceMainList({ ...where });
    return toTree({
      data,
      idField: 'id',
      parentIdField: 'parentId'
    });
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ where });
  };

  /** 重置查询 */
  const reset = () => {
    queryParams.value = {
      spaceCode: '',
      spaceName: '',
      parkName: ''
    };
    reload();
  };

  /** 打开编辑弹窗 */
  const openEdit = (row, id) => {
    current.value = row ?? null;
    parentId.value = id;
    showEdit.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    // 检查是否为顶级节点
    if (!row.parentId || row.parentId === 0) {
      EleMessage.warning('顶级节点不允许删除');
      return;
    }

    ElMessageBox.confirm(
      `是否确认删除名称为"${row.spaceName}"的数据项？`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        deleteSpaceMain(row.id)
          .then(() => {
            loading.close();
            EleMessage.success('删除成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 启用空间 */
  const enableSpace = (row) => {
    ElMessageBox.confirm(
      `是否确认启用名称为"${row.spaceName}"的空间？`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        enabledSpaceMain(row)
          .then(() => {
            loading.close();
            EleMessage.success('启用成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 禁用空间 */
  const disableSpace = (row) => {
    ElMessageBox.confirm(
      `是否确认禁用名称为"${row.spaceName}"的空间及其子空间？`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        disabledSpaceMain(row)
          .then(() => {
            loading.close();
            EleMessage.success('禁用成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 全部启用空间 */
  const enableSpaceAll = (row) => {
    ElMessageBox.confirm(
      `是否确认启用名称为"${row.spaceName}"的空间及其所有子空间？`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        enabledSpaceMain(row)
          .then(() => {
            loading.close();
            EleMessage.success('全部启用成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 展开全部 */
  const expandAll = () => {
    tableRef.value?.toggleRowExpansionAll?.(true);
  };

  /** 折叠全部 */
  const foldAll = () => {
    tableRef.value?.toggleRowExpansionAll?.(false);
  };
</script>
