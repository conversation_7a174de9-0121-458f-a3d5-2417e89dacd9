<!-- 空间信息选择 -->
<template>
  <el-tree-select
    clearable
    check-strictly
    default-expand-all
    :data="data"
    node-key="id"
    :props="{ label: 'spaceName' }"
    :model-value="modelValue"
    :placeholder="placeholder"
    class="ele-fluid"
    @update:modelValue="updateValue"
  />
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import { toTree } from 'ele-admin-plus/es';
  import { getSpaceMainSimpleList } from '@/api/info/spacemain';

  const emit = defineEmits(['update:modelValue']);

  defineProps({
    /** 选中的空间 */
    modelValue: [Number, String],
    /** 提示信息 */
    placeholder: {
      type: String,
      default: '请选择上级空间'
    }
  });

  /** 空间数据 */
  const data = ref([]);

  /** 更新选中数据 */
  const updateValue = (value) => {
    emit('update:modelValue', value);
  };
  onMounted(() => {
    getSpaceMainSimpleList().then((list) => {
      data.value = toTree({
        data: list,
        idField: 'id',
        parentIdField: 'parentId'
      });
    });
  });
</script>
