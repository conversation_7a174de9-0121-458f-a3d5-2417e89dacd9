<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="460"
    :model-value="modelValue"
    :title="isUpdate ? '修改地区' : '添加地区'"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-form-item label="上级地区" prop="parentId">
        <CountryAreaSelect
          v-model="form.parentId"
          placeholder="请选择上级地区"
        />
      </el-form-item>
      <el-form-item label="地区名称" prop="areaName">
        <el-input
          clearable
          :maxlength="20"
          v-model="form.areaName"
          placeholder="请输入地区名称"
        />
      </el-form-item>
      <el-form-item label="地区编码" prop="areaCode">
        <el-input
          clearable
          :maxlength="10"
          v-model="form.areaCode"
          placeholder="请输入地区编码"
        />
      </el-form-item>
      <el-form-item label="级别" prop="level">
        <dict-data
          :code="DICT_TYPE.INFO_COUNTRY_AREA_LEVEL"
          type="select"
          value-type="number"
          v-model="form.level"
        />
      </el-form-item>
      <el-form-item label="地区状态" prop="status">
        <el-select v-model="form.status" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(
              DICT_TYPE.INFO_COUNTRY_AREA_STATUS
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- 级别限制提示 -->
      <el-form-item v-if="isLevelLimit" label="">
        <el-alert
          title="已达到最大级别限制"
          description="当前地区级别已达到4级，不能再添加子级地区"
          type="warning"
          :closable="false"
          show-icon
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button
        type="primary"
        :loading="loading"
        :disabled="isLevelLimit"
        @click="save"
      >
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, watch, computed } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import CountryAreaSelect from './countryarea-select.vue';
  import { createCountryArea, updateCountryArea } from '@/api/info/countryarea';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object,
    /** 上级部门id */
    parentId: Number
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 是否达到级别限制 */
  const isLevelLimit = computed(() => {
    // 如果是新增且父级地区级别为4，则不允许新增
    if (!isUpdate.value && parentLevel.value >= 4) {
      return true;
    }
    return false;
  });

  /** 父级地区级别 */
  const parentLevel = ref(0);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    parentId: void 0,
    areaName: '',
    areaCode: '',
    sourceCode: 'MANUAL',
    level: undefined,
    status: 1
  });

  /** 表单验证规则 */
  const rules = reactive({
    areaName: [
      {
        required: true,
        message: '请输入地区名称',
        type: 'string',
        trigger: 'blur'
      }
    ],
    areaCode: [
      {
        required: true,
        message: '请输入地区编码',
        type: 'string',
        trigger: 'blur'
      }
    ],
    status: [
      {
        required: true,
        message: '请选择状态',
        type: 'number',
        trigger: 'blur'
      }
    ],
    level: [
      {
        required: true,
        message: '请选择级别',
        type: 'number',
        trigger: 'blur'
      }
    ]
  });

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const saveOrUpdate = isUpdate.value
        ? updateCountryArea
        : createCountryArea;
      saveOrUpdate({ ...form, parentId: form.parentId || 0 })
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          assignFields({
            ...props.data,
            parentId: props.data.parentId || void 0
          });
          isUpdate.value = true;
          // 获取父级地区级别
          if (props.data.parentId) {
            getParentLevel(props.data.parentId);
          }
        } else {
          form.parentId = props.parentId;
          isUpdate.value = false;
          // 获取父级地区级别
          if (props.parentId) {
            getParentLevel(props.parentId);
          }
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
        parentLevel.value = 0;
      }
    }
  );

  /** 获取父级地区级别 */
  const getParentLevel = async (_parentId) => {
    try {
      // 这里需要调用API获取父级地区信息
      // 暂时设置为0，实际项目中需要根据API调整
      parentLevel.value = 0;
    } catch (error) {
      console.error('获取父级地区级别失败:', error);
      parentLevel.value = 0;
    }
  };

  // 监听父级地区变化
  watch(
    () => form.parentId,
    (newParentId) => {
      if (newParentId) {
        getParentLevel(newParentId);
      } else {
        parentLevel.value = 0;
      }
    }
  );
</script>
