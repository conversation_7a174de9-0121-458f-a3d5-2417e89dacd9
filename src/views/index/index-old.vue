<template>
  <ele-page class="workplace-page">
    <link-card ref="linkCardRef" />
    <el-row :gutter="16" ref="wrapRef">
      <el-col
        v-for="(item, index) in data"
        :key="item.name"
        :md="item.md"
        :sm="item.sm"
        :xs="item.xs"
      >
        <component
          :is="item.name"
          :title="item.title"
          @command="(command) => onCommand(command, index)"
        />
      </el-col>
    </el-row>
    <ele-card :body-style="{ padding: 0 }">
      <div class="workplace-bottom">
        <el-link
          type="primary"
          :icon="PlusCircleOutlined"
          :underline="false"
          class="workplace-button"
          @click="add"
        >
          添加卡片
        </el-link>
        <el-divider direction="vertical" style="margin: 0" />
        <el-link
          type="primary"
          :icon="UndoOutlined"
          :underline="false"
          class="workplace-button"
          @click="reset"
        >
          重置布局
        </el-link>
      </div>
    </ele-card>
    <!-- 添加卡片弹窗 -->
    <ele-modal :width="680" v-model="visible" title="未添加的卡片">
      <el-row :gutter="16" style="margin-top: -16px">
        <el-col
          v-for="item in notAddedData"
          :key="item.name"
          :md="8"
          :sm="12"
          :xs="24"
        >
          <ele-card
            bordered
            :header="item.title"
            :header-style="{ padding: '8px 14px', fontSize: '14px' }"
            :body-style="{ padding: '32px 0', textAlign: 'center' }"
            style="margin-top: 16px"
          >
            <el-button
              plain
              round
              size="small"
              type="primary"
              @click="addView(item)"
            >
              添加
            </el-button>
          </ele-card>
        </el-col>
      </el-row>
      <el-empty
        v-if="!notAddedData.length"
        description="已添加所有视图"
        :image-size="80"
      />
    </ele-modal>
  </ele-page>
</template>

<script setup>
  import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
  import SortableJs from 'sortablejs';
  import { EleMessage } from 'ele-admin-plus/es';
  import { PlusCircleOutlined, UndoOutlined } from '@/components/icons';
  import LinkCard from './components/link-card.vue';

  const CACHE_KEY = 'workplace-layout';

  /** 默认布局 */
  const DEFAULT = [
    {
      name: 'news-card',
      title: '园区公告',
      md: 8,
      sm: 24,
      xs: 24
    },
    {
      name: 'workflow-card',
      title: '我的任务',
      md: 16,
      sm: 24,
      xs: 24
    },
    {
      name: 'calendar-card',
      title: '日程安排',
      md: 16,
      sm: 24,
      xs: 24
    },
    {
      name: 'notice-card',
      title: '新闻动态',
      md: 8,
      sm: 24,
      xs: 24
    },
    {
      name: 'demo1-card',
      title: '总销售额',
      md: 6,
      sm: 24,
      xs: 24
    },
    {
      name: 'demo2-card',
      title: '访问量',
      md: 6,
      sm: 24,
      xs: 24
    },
    {
      name: 'demo3-card',
      title: '支付笔数',
      md: 6,
      sm: 24,
      xs: 24
    },
    {
      name: 'demo4-card',
      title: '活动运营效果',
      md: 6,
      sm: 24,
      xs: 24
    }
  ];
  /** 初始化默认布局 */
  const INIT = [
    {
      name: 'news-card',
      title: '园区公告',
      md: 8,
      sm: 24,
      xs: 24
    },
    {
      name: 'workflow-card',
      title: '我的任务',
      md: 16,
      sm: 24,
      xs: 24
    },
    {
      name: 'calendar-card',
      title: '日程安排',
      md: 16,
      sm: 24,
      xs: 24
    },
    {
      name: 'notice-card',
      title: '新闻动态',
      md: 8,
      sm: 24,
      xs: 24
    }
  ];
  /** 获取缓存的数据 */
  const getCacheData = () => {
    const str = localStorage.getItem(CACHE_KEY);
    try {
      return str ? JSON.parse(str) : null;
    } catch (e) {
      return null;
    }
  };

  /** 卡片数据 */
  const data = ref([...(getCacheData() ?? INIT)]);

  /** 弹窗是否打开 */
  const visible = ref(false);

  /** 快捷方式卡片 */
  const linkCardRef = ref(null);

  /** 容器 */
  const wrapRef = ref(null);

  /** 拖拽排序实例 */
  let sortableIns = null;

  /** 未添加的数据 */
  const notAddedData = computed(() => {
    return DEFAULT.filter((d) => !data.value.some((t) => t.name === d.name));
  });

  /** 添加 */
  const add = () => {
    visible.value = true;
  };

  /** 重置布局 */
  const reset = () => {
    data.value = [...INIT];
    cacheData();
    linkCardRef.value?.reset?.();
    EleMessage.success('已重置');
  };

  /** 缓存布局 */
  const cacheData = () => {
    localStorage.setItem(CACHE_KEY, JSON.stringify(data.value));
  };

  /** 编辑卡片 */
  const onCommand = (command, index) => {
    switch (command) {
      case 'edit': // 编辑
        EleMessage.info({ message: '点击了编辑', plain: true });
        break;
      case 'remove': // 删除
        data.value = data.value.filter((_d, i) => i !== index);
        cacheData();
        break;
    }
  };

  /** 添加视图 */
  const addView = (item) => {
    data.value.push(item);
    cacheData();
    EleMessage.success('已添加');
  };

  onMounted(() => {
    // 卡片支持拖动排序
    if ('ontouchstart' in document.documentElement) {
      return;
    }
    sortableIns = new SortableJs(wrapRef.value?.$el, {
      handle: '.ele-card-header',
      animation: 300,
      onUpdate: ({ oldIndex, newIndex }) => {
        if (typeof oldIndex === 'number' && typeof newIndex === 'number') {
          const temp = [...data.value];
          temp.splice(newIndex, 0, temp.splice(oldIndex, 1)[0]);
          data.value = temp;
          cacheData();
        }
      },
      setData: () => {},
      forceFallback: true
    });
  });

  onBeforeUnmount(() => {
    sortableIns && sortableIns.destroy();
    sortableIns = null;
  });
</script>

<script>
  import NoticeCard from './components/notice-card.vue';
  import NewsCard from './components/news-card.vue';
  import CalendarCard from './components/calendar-card.vue';
  import WorkflowCard from './components/workflow-card.vue';
  import GoalCard from './components/goal-card.vue';
  import Demo1Card from './components/demo1-card.vue';
  import Demo2Card from './components/demo2-card.vue';
  import Demo3Card from './components/demo3-card.vue';
  import Demo4Card from './components/demo4-card.vue';

  export default {
    name: 'Index',
    components: {
      NoticeCard,
      NewsCard,
      GoalCard,
      CalendarCard,
      WorkflowCard,
      Demo1Card,
      Demo2Card,
      Demo3Card,
      Demo4Card
    }
  };
</script>

<style lang="scss" scoped>
  .workplace-page {
    :deep(.ele-card-header) {
      cursor: move;
    }

    :deep(.el-col) {
      &.sortable-chosen > .ele-card {
        box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 0.2);
      }

      &.sortable-ghost {
        opacity: 0;
      }

      &.sortable-fallback {
        opacity: 1 !important;
      }
    }
  }

  /* 底部按钮 */
  .workplace-bottom {
    display: flex;
    align-items: center;

    .workplace-button {
      flex: 1;
      padding: 10px 0;
      transition: background-color 0.2s;

      &:hover {
        background: hsla(0, 0%, 60%, 0.05);
      }

      :deep(.el-icon) {
        font-size: 15px;
        margin: -1px 6px 0 0;
      }
    }
  }
</style>
