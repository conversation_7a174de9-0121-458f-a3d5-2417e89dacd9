<!-- 项目进度 -->
<template>
  <ele-card :header="title" :body-style="{ padding: '10px', height: '370px' }">
    <template #extra>
      <div style="margin-right: 15px">
        <el-link
          type="primary"
          @click="moreWork"
          :underline="false"
          target="_blank"
        >
          更多
        </el-link>
      </div>
      <more-icon @command="onCommand" />
    </template>
    <ele-tabs
      ref="tabRef"
      :items="items"
      v-model="active"
      :mousewheel="true"
      @tab-click="handleTabClick"
    >
      <template #first>
        <div style="padding: 10px 0">
          <ele-pro-table
            :height="300"
            row-key="id"
            ref="tableRef1"
            :columns="taskTodoColumns"
            :datasource="taskTodoList"
            :show-overflow-tooltip="true"
            :pagination="false"
            :toolbar="false"
            :bottom-line="false"
            size="default"
            class="taskTodo-table"
          >
            <template #projectName="{ row }">
              <el-link
                type="primary"
                :underline="false"
                @click="toDoHandleAudit(row)"
              >
                {{ row.processInstance.name }}
              </el-link>
            </template>
            <template #waitTime="{ row }">
              <ele-text v-if="formatPast4(row.createTime) === 3" type="success">
                {{ formatPast3(row.createTime) }}</ele-text
              >
              <ele-text
                v-else-if="formatPast4(row.createTime) === 2"
                type="warning"
              >
                {{ formatPast3(row.createTime) }}
              </ele-text>
              <ele-text
                v-else-if="formatPast4(row.createTime) == 1"
                type="danger"
                >{{ formatPast3(row.createTime) }}</ele-text
              >
            </template>
            <template #progress="{ row }">
              <el-button
                size="small"
                @click="toDoHandleAudit(row)"
                type="primary"
                >办理</el-button
              >
            </template>
          </ele-pro-table>
        </div>
      </template>

      <template #second>
        <div style="padding: 10px 0">
          <ele-pro-table
            :height="300"
            row-key="id"
            ref="tableRef2"
            :columns="taskDoneColumns"
            :datasource="taskDoneList"
            :show-overflow-tooltip="true"
            :load-on-created="false"
            :pagination="false"
            :toolbar="false"
            :bottom-line="false"
            size="default"
            class="taskDone-table"
          >
            <template #projectName="{ row }">
              <el-link
                type="primary"
                @click="taskDoneHandleAudit(row)"
                :underline="false"
              >
                {{ row.processInstance.name }}
              </el-link>
            </template>
            <template #durationInMillis="{ row }">
              {{ formatPast2(row.durationInMillis) }}
            </template>
            <template #status="{ row }">
              <dict-data
                type="tag"
                :code="DICT_TYPE.BPM_TASK_STATUS"
                :model-value="row.status"
              />
            </template>
            <template #progress="{ row }">
              <el-button
                size="small"
                type="primary"
                @click="taskDoneHandleAudit(row)"
                >详细</el-button
              >
            </template>
          </ele-pro-table>
        </div>
      </template>
      <template #third>
        <div style="padding: 10px 0">
          <ele-pro-table
            :height="300"
            row-key="id"
            ref="tableRef3"
            :columns="taskCopyColumns"
            :datasource="taskCopyList"
            :load-on-created="false"
            :show-overflow-tooltip="true"
            :pagination="false"
            :toolbar="false"
            :bottom-line="false"
            size="default"
            class="project-table"
          >
            <template #projectName="{ row }">
              <el-link
                type="primary"
                @click="taskCopyhandleAudit(row)"
                :underline="false"
              >
                {{ row.processInstanceName }}
              </el-link>
            </template>
            <template #progress="{ row }">
              <el-button
                size="small"
                type="primary"
                @click="taskCopyhandleAudit(row)"
                >详情</el-button
              >
            </template>
          </ele-pro-table>
        </div>
      </template>
      <template #fourth>
        <div style="padding: 10px 0">
          <ele-pro-table
            :height="300"
            row-key="id"
            ref="tableRef4"
            :columns="taskMyColumns"
            :datasource="taskMyList"
            :load-on-created="false"
            :show-overflow-tooltip="true"
            :pagination="false"
            :toolbar="false"
            :bottom-line="false"
            size="default"
            class="project-table"
          >
            <template #projectName="{ row }">
              <el-link
                type="primary"
                :underline="false"
                @click="taskMyhandleAudit(row)"
              >
                {{ row.name }}
              </el-link>
            </template>
            <template #currentTask="{ row }">
              <el-button
                type="primary"
                v-for="task in row.tasks"
                :key="task.id"
                link
              >
                <span>{{ task.name }}</span>
              </el-button>
            </template>
            <template #status="{ row }">
              <dict-data
                :code="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS"
                type="tag"
                :model-value="row.status"
              />
            </template>
            <template #progress="{ row }">
              <el-button
                size="small"
                type="primary"
                @click="taskMyhandleAudit(row)"
                >详细</el-button
              >
            </template>
          </ele-pro-table>
        </div>
      </template>
    </ele-tabs>
  </ele-card>
</template>

<script setup>
  import { ref } from 'vue';
  import MoreIcon from './more-icon.vue';
  import * as TaskApi from '@/api/bpm/task';
  import * as ProcessInstanceApi from '@/api/bpm/processInstance';
  import { DICT_TYPE } from '@/utils/dict';
  import {
    dateFormatter,
    formatPast2,
    formatPast3,
    formatPast4
  } from '@/utils/formatTime';

  defineProps({
    title: String
  });
  const emit = defineEmits(['command']);

  const { push, router } = useRouter(); // 路由
  /** 表格实例 */
  const tableRef1 = ref(null);
  const tableRef2 = ref(null);
  const tableRef3 = ref(null);
  const tableRef4 = ref(null);
  /** 搜索 */
  const reload1 = () => {
    tableRef1.value?.reload?.({
      pageNo: 1,
      pageSize: 6
    });
  };
  /** 搜索 */
  const reload2 = () => {
    tableRef2.value?.reload?.({
      pageNo: 1,
      pageSize: 6
    });
  };
  /** 搜索 */
  const reload3 = () => {
    tableRef3.value?.reload?.({
      pageNo: 1,
      pageSize: 6
    });
  };
  /** 搜索 */
  const reload4 = () => {
    tableRef4.value?.reload?.({
      pageNo: 1,
      pageSize: 6
    });
  };
  const taskTodoColumns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 40,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'processInstance.name',
      label: '流程名称',
      align: 'center',
      minWidth: 200,
      slot: 'projectName'
    },
    {
      prop: 'processInstance.startUser.nickname',
      label: '发起人',
      align: 'center',
      minWidth: 100
    },
    {
      prop: 'createTime',
      label: '发起时间',
      align: 'center',
      minWidth: 180,
      formatter: dateFormatter
    },
    {
      prop: 'waitTime',
      label: '滞留时间',
      align: 'center',
      minWidth: 180,
      slot: 'waitTime'
    },
    {
      prop: 'progress',
      label: '操作',
      width: 100,
      align: 'center',
      slot: 'progress',
      showOverflowTooltip: false
    }
  ]);
  const taskDoneColumns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 40,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'processInstance.name',
      label: '流程名称',
      align: 'center',
      minWidth: 200,
      slot: 'projectName'
    },
    {
      prop: 'processInstance.startUser.nickname',
      label: '发起人',
      align: 'center',
      minWidth: 80
    },
    {
      prop: 'createTime',
      label: '发起时间',
      align: 'center',
      minWidth: 180,
      formatter: dateFormatter
    },
    {
      prop: 'status',
      label: '审批状态',
      align: 'center',
      minWidth: 90,
      slot: 'status'
    },
    {
      prop: 'durationInMillis',
      label: '处理耗时',
      align: 'center',
      minWidth: 90,
      slot: 'durationInMillis'
    },
    {
      prop: 'progress',
      label: '操作',
      width: 100,
      align: 'center',
      slot: 'progress',
      showOverflowTooltip: false
    }
  ]);
  const taskCopyColumns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 40,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'processInstanceName',
      label: '流程名',
      align: 'center',
      minWidth: 200,
      slot: 'projectName'
    },
    {
      prop: 'taskName',
      label: '抄送任务',
      align: 'center',
      minWidth: 180
    },
    {
      prop: 'creatorName',
      label: '抄送人',
      align: 'center',
      minWidth: 80
    },
    {
      prop: 'createTime',
      label: '抄送时间',
      align: 'center',
      minWidth: 180,
      formatter: dateFormatter
    },
    {
      prop: 'progress',
      label: '操作',
      width: 100,
      align: 'center',
      slot: 'progress',
      showOverflowTooltip: false
    }
  ]);
  const taskMyColumns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 40,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'name',
      label: '流程名称',
      align: 'center',
      minWidth: 200,
      slot: 'projectName'
    },
    {
      prop: 'categoryName',
      label: '流程分类',
      align: 'center',
      minWidth: 100
    },
    {
      prop: 'status',
      label: '流程状态',
      align: 'center',
      minWidth: 90,
      slot: 'status'
    },
    {
      prop: 'currentTask',
      label: '当前审批节点',
      align: 'center',
      minWidth: 120,
      slot: 'currentTask'
    },
    {
      prop: 'startTime',
      label: '发起时间',
      align: 'center',
      minWidth: 180,
      formatter: dateFormatter
    },
    {
      prop: 'progress',
      label: '操作',
      width: 100,
      align: 'center',
      slot: 'progress',
      showOverflowTooltip: false
    }
  ]);
  const projectList = ref([]);
  /** 待办清单 */
  const taskTodoList = () => {
    return TaskApi.getTaskTodoPage({
      pageNo: 1,
      pageSize: 6
    });
  };
  /** 已办清单 */
  const taskDoneList = () => {
    return TaskApi.getTaskDonePage({
      pageNo: 1,
      pageSize: 6
    });
  };

  /** 抄送清单 */
  const taskCopyList = () => {
    return ProcessInstanceApi.getProcessInstanceCopyPage({
      pageNo: 1,
      pageSize: 6
    });
  };

  /** 我的流程清单 */
  const taskMyList = () => {
    return ProcessInstanceApi.getProcessInstanceMyPage({
      pageNo: 1,
      pageSize: 6
    });
  };

  const handleTabClick = (pane) => {
    getListData(pane.paneName);
  };
  const getListData = async (paneName) => {
    if (paneName == 'first') {
      reload1();
    } else if (paneName == 'second') {
      reload2();
    } else if (paneName == 'third') {
      reload3();
    } else {
      reload4();
    }
  };

  /** 选中 */
  const active = ref('first');

  /** 标签页数据 */
  const items = ref([
    {
      name: 'first',
      label: '我的待办'
    },
    {
      name: 'second',
      label: '我的已办'
    },
    {
      name: 'third',
      label: '我的抄送'
    },
    {
      name: 'fourth',
      label: '我的申请'
    }
  ]);
  const moreWork = () => {
    let path = '';
    debugger;
    if (active.value == 'first') {
      path = '/task/todo';
    }
    if (active.value == 'second') {
      path = '/task/done';
    }
    if (active.value == 'third') {
      path = '/task/copy';
    }
    if (active.value == 'fourth') {
      path = '/task/my';
    }
    push({
      path: path
    });
  };
  const onCommand = (command) => {
    emit('command', command);
  };

  /** 处理审批按钮 */
  const toDoHandleAudit = (row) => {
    push({
      name: 'BpmProcessInstanceTodoDetail',
      query: {
        id: row.processInstance.id,
        taskId: row.id
      }
    });
  };
  /** 详细按钮 */
  const taskDoneHandleAudit = (row) => {
    push({
      name: 'BpmProcessInstanceDoneDetail',
      query: {
        id: row.processInstance.id,
        taskId: row.id
      }
    });
  };
  //抄送详细
  const taskCopyhandleAudit = (row) => {
    const query = {
      id: row.processInstanceId,
      activityId: undefined
    };
    if (row.activityId) {
      query.activityId = row.activityId;
    }
    push({
      name: 'BpmProcessInstanceCopyDetail',
      query: query
    });
  };
  //申请详细
  const taskMyhandleAudit = (row) => {
    push({
      name: 'BpmProcessInstanceDetail',
      query: {
        id: row.id
      }
    });
  };
</script>

<style lang="scss" scoped>
  .project-table :deep(.el-progress__text) {
    font-size: 12px !important;
  }
</style>
