<!-- 本月目标 -->
<template>
  <ele-card class="statistics-card" :header="title">
    <template #extra>
      <more-icon @command="onCommand" />
    </template>
    <ele-text size="xxl" class="statistics-value">¥ 126,560</ele-text>
    <div class="statistics-body">
      <div class="statistics-trend-text">
        <div>周同比12%</div>
        <ele-text type="danger" :icon="CaretUpFilled" />
      </div>
      <div class="statistics-trend-text">
        <div>日同比11%</div>
        <ele-text type="success" :icon="CaretDownFilled" />
      </div>
    </div>
    <el-divider />
    <div>日销售额 ￥12,423</div>
  </ele-card>
</template>

<script setup>
  import MoreIcon from './more-icon.vue';
  import {
    QuestionCircleOutlined,
    CaretUpFilled,
    CaretDownFilled
  } from '@/components/icons';
  defineProps({
    title: String
  });

  const emit = defineEmits(['command']);

  const onCommand = (command) => {
    emit('command', command);
  };
</script>

<style lang="scss" scoped>
  .statistics-card {
    :deep(.ele-card-body) {
      padding: 16px 22px 12px 22px;
    }

    :deep(.el-divider) {
      margin: 12px 0;
      opacity: 0.6;
    }

    .statistics-header {
      display: flex;
      align-items: center;

      .statistics-header-text {
        flex: 1;
      }

      .statistics-header-tip {
        font-size: 15px;
        cursor: help;
      }
    }

    .statistics-value {
      margin-top: 4px;
    }

    .statistics-body {
      height: 36px;
      display: flex;
      padding-top: 18px;
      box-sizing: border-box;
    }

    .statistics-trend-text {
      display: flex;
      align-items: center;
      white-space: nowrap;
      word-break: break-all;
      overflow: hidden;

      .el-icon {
        font-size: 16px;
        margin-left: 4px;
      }

      & + .statistics-trend-text {
        margin-left: 14px;
      }
    }

    .statistics-footer {
      display: flex;
      align-items: center;
    }
  }
</style>
