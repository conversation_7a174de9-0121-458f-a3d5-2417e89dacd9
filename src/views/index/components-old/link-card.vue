<template>
  <ele-card
    header="外部系统导航"
    shadow="always"
    class="link-card-container"
    :body-style="{ padding: '3px 5px', height: '105px' }"
  >
    <el-row :gutter="16" ref="wrapRef" class="row-no-wrap">
      <el-col
        v-for="item in data"
        :key="item.id"
        :md="2"
        :sm="6"
        :xs="12"
        class="inline-col"
      >
        <ele-card
          shadow="hover"
          :body-style="{ padding: '0', height: '80px' }"
          class="inner-card"
        >
          <view @click="goto(item.url)" class="app-link">
            <el-icon
              class="app-link-icon"
              :style="{ backgroundColor: item.color, color: 'white' }"
            >
              <component :is="item.icon" />
            </el-icon>
            <div class="app-link-title">{{ item.name }}</div>
          </view>
        </ele-card>
      </el-col>
    </el-row>
  </ele-card>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import * as LinkApi from '@/api/system/link';
  const goto = (url) => {
    window.open(url);
  };
  const data = ref([]);
  /** 根节点 */
  const wrapRef = ref(null);

  /** 重置布局 */
  const reset = () => {};
  /** 加载数据 */
  const getList = async () => {
    data.value = await LinkApi.getLinkList();
  };
  onMounted(() => {
    getList();
  });

  defineExpose({ reset });
</script>

<script>
  import * as MenuIcons from '@/layout/menu-icons';
  export default {
    components: MenuIcons
  };
</script>

<style lang="scss" scoped>
  .link-card-container {
    width: 100%;
    margin-bottom: 10px;
    padding: 2px;
    border-radius: 8px;
    white-space: nowrap;
  }

  .row-no-wrap {
    display: flex;
    flex-wrap: nowrap;
    margin-left: -8px; // 减少左边距
    margin-right: -8px; // 减少右边距
  }

  .inline-col {
    display: inline-block;
    vertical-align: top;
    margin-bottom: 0; // 移除底部外边距
    padding-left: 8px; // 增加左边距
    padding-right: 8px; // 增加右边距
  }

  .inner-card {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease;
    cursor: pointer;

    &:hover {
      /* transform: translateY(-5px); */ // 注释掉或移除这一行
      /* box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1); */ // 注释掉或移除这一行
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); // 使用较小的阴影
    }
  }

  .app-link {
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    text-decoration: none;
    color: inherit;
    border-radius: 4px;
    transition: all 0.3s ease;
    width: 100%;
    height: 100%;
    box-sizing: border-box; // 确保内边距和边框包含在宽度内
  }

  .app-link-icon {
    font-size: 33px;
    background-color: transparent; // 移除默认背景色
    padding: 8px; // 增加内边距以适应背景色
    border-radius: 50%; // 使其成为圆形
    display: inline-flex;
    justify-content: center;
    align-items: center;
    margin: 6px 0 4px 0; // 减少底部外边距

    & > svg {
      stroke-width: 3;
    }
  }

  .app-link-title {
    font-size: 15px;
    margin-top: 4px; // 减少顶部外边距
    font-weight: 400;
  }

  .el-col.sortable-ghost {
    opacity: 0;
  }
</style>
