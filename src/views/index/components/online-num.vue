<template>
  <ele-card class="publishing-status-card">
    <template #header>
      <div class="card-header-custom">
        <div class="header-left">
          <div class="header-icon-wrapper">
            <el-icon class="header-icon"><VideoPlay /></el-icon>
          </div>
          <span class="header-title">发布监控中心</span>
        </div>
        <div class="header-right">
          <div class="status-indicator">
            <div class="status-dot"></div>
            <span>系统正常</span>
          </div>
        </div>
      </div>
    </template>

    <div class="publishing-content">
      <!-- 核心指标面板 -->
      <div class="metrics-panel">
        <div class="metric-card primary">
          <div class="metric-header">
            <div class="metric-icon">
              <el-icon><Promotion /></el-icon>
            </div>
            <div class="metric-trend positive">
              <el-icon><TrendCharts /></el-icon>
              <span>+8.5%</span>
            </div>
          </div>
          <div class="metric-body">
            <div class="metric-value">1,240</div>
            <div class="metric-label">正在发布</div>
            <div class="metric-progress">
              <div class="progress-track">
                <div class="progress-fill" style="width: 75%"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="metric-card success">
          <div class="metric-header">
            <div class="metric-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="metric-trend positive">
              <el-icon><TrendCharts /></el-icon>
              <span>+12.3%</span>
            </div>
          </div>
          <div class="metric-body">
            <div class="metric-value">9,070</div>
            <div class="metric-label">发布完成</div>
            <div class="metric-progress">
              <div class="progress-track">
                <div class="progress-fill success" style="width: 92%"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="metric-card warning">
          <div class="metric-header">
            <div class="metric-icon">
              <el-icon><View /></el-icon>
            </div>
            <div class="metric-trend positive">
              <el-icon><TrendCharts /></el-icon>
              <span>+15.7%</span>
            </div>
          </div>
          <div class="metric-body">
            <div class="metric-value">98.5%</div>
            <div class="metric-label">播放成功率</div>
            <div class="metric-progress">
              <div class="progress-track">
                <div class="progress-fill warning" style="width: 98%"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="metric-card info">
          <div class="metric-header">
            <div class="metric-icon">
              <el-icon><ChatDotSquare /></el-icon>
            </div>
            <div class="metric-trend positive">
              <el-icon><TrendCharts /></el-icon>
              <span>+22.1%</span>
            </div>
          </div>
          <div class="metric-body">
            <div class="metric-value">67.3%</div>
            <div class="metric-label">用户互动率</div>
            <div class="metric-progress">
              <div class="progress-track">
                <div class="progress-fill info" style="width: 67%"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 发布队列和实时统计 -->
      <div class="data-panels">
        <div class="publish-queue">
          <div class="queue-header">
            <h4>发布队列</h4>
            <div class="queue-count">237 条</div>
          </div>
          <div class="queue-list">
            <div class="queue-item" v-for="item in queueItems" :key="item.id">
              <div class="queue-icon" :class="`type-${item.type}`">
                <el-icon><component :is="item.icon" /></el-icon>
              </div>
              <div class="queue-content">
                <div class="queue-title">{{ item.title }}</div>
                <div class="queue-meta">
                  <span class="queue-type">{{ item.typeLabel }}</span>
                  <span class="queue-time">{{ item.time }}</span>
                </div>
              </div>
              <div class="queue-status" :class="`status-${item.status}`">
                {{ getStatusText(item.status) }}
              </div>
            </div>
          </div>
        </div>

        <div class="realtime-stats">
          <div class="stats-header">
            <h4>实时数据</h4>
            <div class="refresh-indicator">
              <el-icon class="rotating"><Refresh /></el-icon>
              <span>{{ currentTime }}</span>
            </div>
          </div>
          <div class="stats-grid">
            <div
              class="stat-item"
              v-for="stat in realtimeStats"
              :key="stat.label"
            >
              <div class="stat-icon" :class="stat.type">
                <el-icon><component :is="stat.icon" /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 趋势图表 -->
      <div class="chart-section">
        <div class="chart-header">
          <h4>7日趋势分析</h4>
          <div class="chart-legend">
            <div class="legend-item">
              <div class="legend-dot success"></div>
              <span>播放成功率</span>
            </div>
            <div class="legend-item">
              <div class="legend-dot engagement"></div>
              <span>用户互动率</span>
            </div>
          </div>
        </div>
        <div class="trend-chart" ref="trendChart"></div>
      </div>

      <!-- 最后更新时间 -->
      <div class="update-time">
        <el-icon><Refresh /></el-icon>
        <span>{{ lastUpdateTime }}</span>
      </div>
    </div>
  </ele-card>
</template>

<script setup>
  import { ref, reactive, onMounted, onUnmounted, watch, nextTick } from 'vue';
  import * as echarts from 'echarts';
  import {
    VideoPlay,
    Promotion,
    CircleCheck,
    Clock,
    Refresh,
    Document,
    Picture,
    Film,
    View,
    ChatDotSquare,
    TrendCharts,
    Timer,
    User
  } from '@element-plus/icons-vue';

  const lastUpdateTime = ref('');
  const currentTime = ref('');
  let timeInterval = null;
  let chartInstance = null;
  const trendChart = ref(null);

  // 监听主题变化
  const isDark = ref(document.documentElement.classList.contains('dark'));

  const themeObserver = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (
        mutation.type === 'attributes' &&
        mutation.attributeName === 'class'
      ) {
        const newIsDark = document.documentElement.classList.contains('dark');
        if (newIsDark !== isDark.value) {
          isDark.value = newIsDark;
          // 主题变化时重新初始化图表
          nextTick(() => {
            if (chartInstance) {
              chartInstance.dispose();
              initTrendChart();
            }
          });
        }
      }
    });
  });

  // 队列项目数据
  const queueItems = reactive([
    {
      id: 1,
      title: '营销活动推广视频',
      type: 'video',
      typeLabel: '视频',
      time: '预计2分钟',
      status: 'waiting',
      icon: Film
    },
    {
      id: 2,
      title: '产品宣传图片',
      type: 'image',
      typeLabel: '图片',
      time: '预计1分钟',
      status: 'processing',
      icon: Picture
    },
    {
      id: 3,
      title: '公司新闻通知',
      type: 'text',
      typeLabel: '文本',
      time: '预计30秒',
      status: 'waiting',
      icon: Document
    }
  ]);

  // 实时统计数据
  const realtimeStats = reactive([
    { label: '发布速度', value: '156/min', icon: Timer, type: 'primary' },
    { label: '在线用户', value: '2.3w', icon: User, type: 'success' },
    { label: '平均延迟', value: '2.3s', icon: Clock, type: 'warning' },
    { label: '错误率', value: '0.1%', icon: View, type: 'danger' }
  ]);

  // 获取状态文本
  const getStatusText = (status) => {
    switch (status) {
      case 'waiting':
        return '等待中';
      case 'processing':
        return '处理中';
      case 'completed':
        return '已完成';
      default:
        return '未知';
    }
  };

  // 初始化趋势图表
  const initTrendChart = () => {
    if (trendChart.value) {
      chartInstance = echarts.init(trendChart.value);
      chartInstance.setOption({
        backgroundColor: 'transparent',
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
          axisLine: {
            lineStyle: {
              color: 'var(--el-border-color)'
            }
          },
          axisLabel: {
            color: 'var(--el-text-color-regular)',
            fontSize: 12
          },
          axisTick: {
            lineStyle: {
              color: 'var(--el-border-color)'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: 'var(--el-border-color)'
            }
          },
          axisLabel: {
            color: 'var(--el-text-color-regular)',
            fontSize: 12
          },
          axisTick: {
            lineStyle: {
              color: 'var(--el-border-color)'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'var(--el-border-color-lighter)',
              type: 'dashed'
            }
          }
        },
        series: [
          {
            name: '播放成功率',
            type: 'line',
            data: [95, 97, 94, 98, 96, 99, 98],
            smooth: true,
            lineStyle: {
              color: 'var(--el-color-success)',
              width: 3
            },
            itemStyle: {
              color: 'var(--el-color-success)',
              borderWidth: 2,
              borderColor: '#fff'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'var(--el-color-success-light-8)' },
                  { offset: 1, color: 'transparent' }
                ]
              }
            }
          },
          {
            name: '用户互动率',
            type: 'line',
            data: [65, 68, 72, 70, 75, 73, 67],
            smooth: true,
            lineStyle: {
              color: 'var(--el-color-primary)',
              width: 3
            },
            itemStyle: {
              color: 'var(--el-color-primary)',
              borderWidth: 2,
              borderColor: '#fff'
            }
          }
        ],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'var(--el-bg-color-overlay)',
          borderColor: 'var(--el-border-color)',
          borderWidth: 1,
          textStyle: {
            color: 'var(--el-text-color-primary)',
            fontSize: 12
          },
          extraCssText:
            'box-shadow: var(--el-box-shadow-light); border-radius: 6px;',
          axisPointer: {
            lineStyle: {
              color: 'var(--el-border-color)'
            }
          }
        }
      });
    }
  };

  // 更新时间
  const updateTime = () => {
    const now = new Date();
    lastUpdateTime.value = `最后更新: ${now.toLocaleTimeString()}`;
    currentTime.value = now.toLocaleTimeString();
  };

  onMounted(() => {
    updateTime();
    initTrendChart();
    timeInterval = setInterval(updateTime, 1000);

    // 启动主题观察器
    themeObserver.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });
  });

  onUnmounted(() => {
    if (timeInterval) {
      clearInterval(timeInterval);
    }
    if (chartInstance) {
      chartInstance.dispose();
    }
    // 清理主题观察器
    themeObserver.disconnect();
  });

  defineOptions({ name: 'PublishingStatusCard' });
</script>

<style scoped>
  .publishing-status-card {
    height: 100%;
    background: var(--el-bg-color-page);
    border: 1px solid var(--el-border-color);
  }

  .card-header-custom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .header-icon-wrapper {
    width: 32px;
    height: 32px;
    background: var(--el-color-primary);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .header-icon {
    font-size: 16px;
    color: white;
  }

  .header-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .header-right .status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--el-text-color-regular);
  }

  .status-dot {
    width: 6px;
    height: 6px;
    background: var(--el-color-success);
    border-radius: 50%;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  .publishing-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: calc(100% - 60px);
  }

  /* 核心指标面板 */
  .metrics-panel {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
  }

  .metric-card {
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    border-radius: 12px;
    padding: 16px;
    transition: all 0.3s ease;
  }

  .metric-card:hover {
    box-shadow: 0 4px 12px var(--el-box-shadow-light);
  }

  .metric-card.primary {
    border-left: 4px solid var(--el-color-primary);
  }

  .metric-card.success {
    border-left: 4px solid var(--el-color-success);
  }

  .metric-card.warning {
    border-left: 4px solid var(--el-color-warning);
  }

  .metric-card.info {
    border-left: 4px solid var(--el-color-info);
  }

  .metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .metric-icon {
    width: 28px;
    height: 28px;
    color: var(--el-color-primary);
    font-size: 16px;
  }

  .metric-card.success .metric-icon {
    color: var(--el-color-success);
  }

  .metric-card.warning .metric-icon {
    color: var(--el-color-warning);
  }

  .metric-card.info .metric-icon {
    color: var(--el-color-info);
  }

  .metric-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
  }

  .metric-trend.positive {
    color: var(--el-color-success);
  }

  .metric-body {
    text-align: center;
  }

  .metric-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--el-text-color-primary);
    line-height: 1;
    margin-bottom: 4px;
  }

  .metric-label {
    font-size: 12px;
    color: var(--el-text-color-regular);
    margin-bottom: 8px;
  }

  .metric-progress {
    width: 100%;
  }

  .progress-track {
    width: 100%;
    height: 4px;
    background: var(--el-fill-color);
    border-radius: 2px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: var(--el-color-primary);
    border-radius: 2px;
    transition: width 0.3s ease;
  }

  .progress-fill.success {
    background: var(--el-color-success);
  }

  .progress-fill.warning {
    background: var(--el-color-warning);
  }

  .progress-fill.info {
    background: var(--el-color-info);
  }

  /* 数据面板 */
  .data-panels {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
  }

  .publish-queue,
  .realtime-stats {
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    border-radius: 12px;
    padding: 16px;
  }

  .queue-header,
  .stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .queue-header h4,
  .stats-header h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }

  .queue-count {
    background: var(--el-color-primary-light-8);
    color: var(--el-color-primary);
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
  }

  .refresh-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--el-text-color-regular);
  }

  .rotating {
    animation: rotate 2s linear infinite;
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .queue-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 200px;
    overflow-y: auto;
  }

  .queue-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    background: var(--el-fill-color-lighter);
    transition: background 0.3s ease;
  }

  .queue-item:hover {
    background: var(--el-fill-color-light);
  }

  .queue-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
  }

  .queue-icon.type-video {
    background: var(--el-color-danger);
  }

  .queue-icon.type-image {
    background: var(--el-color-warning);
  }

  .queue-icon.type-text {
    background: var(--el-color-info);
  }

  .queue-content {
    flex: 1;
  }

  .queue-title {
    font-size: 13px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    line-height: 1.2;
  }

  .queue-meta {
    display: flex;
    gap: 8px;
    margin-top: 4px;
  }

  .queue-type,
  .queue-time {
    font-size: 11px;
    color: var(--el-text-color-placeholder);
  }

  .queue-status {
    font-size: 11px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 12px;
  }

  .queue-status.status-waiting {
    background: var(--el-color-info-light-8);
    color: var(--el-color-info);
  }

  .queue-status.status-processing {
    background: var(--el-color-warning-light-8);
    color: var(--el-color-warning);
  }

  .stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    background: var(--el-fill-color-lighter);
  }

  .stat-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
  }

  .stat-icon.primary {
    background: var(--el-color-primary);
  }

  .stat-icon.success {
    background: var(--el-color-success);
  }

  .stat-icon.warning {
    background: var(--el-color-warning);
  }

  .stat-icon.danger {
    background: var(--el-color-danger);
  }

  .stat-content {
    flex: 1;
  }

  .stat-value {
    font-size: 16px;
    font-weight: 700;
    color: var(--el-text-color-primary);
    line-height: 1;
  }

  .stat-label {
    font-size: 11px;
    color: var(--el-text-color-regular);
    margin-top: 2px;
  }

  /* 图表区域 */
  .chart-section {
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    border-radius: 12px;
    padding: 16px;
  }

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .chart-header h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }

  .chart-legend {
    display: flex;
    gap: 16px;
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--el-text-color-regular);
  }

  .legend-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  .legend-dot.success {
    background: var(--el-color-success);
  }

  .legend-dot.engagement {
    background: var(--el-color-primary);
  }

  .trend-chart {
    height: 200px;
  }

  /* 更新时间 */
  .update-time {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 12px;
    color: var(--el-text-color-placeholder);
    padding: 8px;
    border-top: 1px solid var(--el-border-color-lighter);
  }

  /* 响应式设计 */
  @media (max-width: 1400px) {
    .metrics-panel {
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
    }

    .data-panels {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }

  @media (max-width: 768px) {
    .metrics-panel {
      grid-template-columns: 1fr;
    }

    .stats-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
