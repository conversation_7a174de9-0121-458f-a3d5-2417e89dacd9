<template>
  <ele-card class="content-analysis-card">
    <template #header>
      <div class="card-header-custom">
        <div class="header-left">
          <div class="header-icon-wrapper">
            <el-icon class="header-icon"><DataLine /></el-icon>
          </div>
          <span class="header-title">内容发布效果分析</span>
        </div>
        <div class="header-right">
          <div class="status-indicator">
            <div class="status-dot"></div>
            <span>实时监控</span>
          </div>
        </div>
      </div>
    </template>

    <div class="analysis-content">
      <!-- 核心指标网格 -->
      <div class="metrics-grid">
        <div
          class="metric-card"
          v-for="(metric, index) in coreMetrics"
          :key="index"
        >
          <div class="metric-header">
            <div class="metric-icon" :class="`icon-${metric.type}`">
              <component :is="metric.icon" />
            </div>
            <div
              class="metric-trend"
              :class="metric.trend > 0 ? 'positive' : 'negative'"
            >
              <el-icon
                ><CaretTop v-if="metric.trend > 0" /><CaretBottom v-else
              /></el-icon>
              <span>{{ Math.abs(metric.trend) }}%</span>
            </div>
          </div>
          <div class="metric-body">
            <div class="metric-value">{{ metric.value }}</div>
            <div class="metric-label">{{ metric.label }}</div>
            <div class="metric-progress">
              <div class="progress-track">
                <div
                  class="progress-fill"
                  :style="{
                    width: metric.progress + '%',
                    background: metric.color
                  }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 趋势分析图表 -->
      <div class="chart-section">
        <div class="chart-header">
          <h4>7日趋势分析</h4>
          <div class="chart-legend">
            <div class="legend-item">
              <div class="legend-dot success"></div>
              <span>播放成功率</span>
            </div>
            <div class="legend-item">
              <div class="legend-dot engagement"></div>
              <span>用户互动率</span>
            </div>
          </div>
        </div>
        <div class="trend-chart" ref="trendChart"></div>
      </div>

      <!-- 实时数据流 -->
      <div class="realtime-stats">
        <div class="stats-header">
          <h4>实时数据</h4>
          <div class="refresh-indicator">
            <el-icon class="rotating"><Refresh /></el-icon>
            <span>{{ currentTime }}</span>
          </div>
        </div>
        <div class="stats-grid">
          <div
            class="stat-item"
            v-for="(stat, index) in realtimeStats"
            :key="index"
          >
            <div class="stat-icon">
              <component :is="stat.icon" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
            <div
              class="stat-change"
              :class="stat.change > 0 ? 'positive' : 'negative'"
            >
              {{ stat.change > 0 ? '+' : '' }}{{ stat.change }}%
            </div>
          </div>
        </div>
      </div>
    </div>
  </ele-card>
</template>

<script setup>
  import { ref, reactive, onMounted, onUnmounted } from 'vue';
  import * as echarts from 'echarts';
  import {
    DataLine,
    View,
    Timer,
    User,
    TrendCharts,
    CaretTop,
    CaretBottom,
    Refresh,
    VideoPlay,
    ChatLineRound,
    Share
  } from '@element-plus/icons-vue';

  const trendChart = ref(null);
  let chartInstance = null;
  let timeInterval = null;

  const currentTime = ref('');

  // 核心指标数据
  const coreMetrics = reactive([
    {
      type: 'success',
      icon: View,
      value: '98.5%',
      label: '播放成功率',
      progress: 98.5,
      trend: 2.3,
      color: 'linear-gradient(135deg, #00d4ff, #4facfe)'
    },
    {
      type: 'duration',
      icon: Timer,
      value: '4.2h',
      label: '平均播放时长',
      progress: 75,
      trend: 5.8,
      color: 'linear-gradient(135deg, #ff9f43, #ff6b6b)'
    },
    {
      type: 'coverage',
      icon: User,
      value: '89.3w',
      label: '覆盖用户',
      progress: 89,
      trend: 12.4,
      color: 'linear-gradient(135deg, #00d4aa, #00ff88)'
    }
  ]);

  // 实时统计数据
  const realtimeStats = reactive([
    {
      icon: VideoPlay,
      value: '1,247',
      label: '今日发布',
      change: 8.5
    },
    {
      icon: ChatLineRound,
      value: '15.6%',
      label: '互动率',
      change: 2.3
    },
    {
      icon: Share,
      value: '92.8%',
      label: '完成率',
      change: -1.2
    }
  ]);

  // 更新时间
  const updateTime = () => {
    const now = new Date();
    currentTime.value = now.toLocaleTimeString();
  };

  // 初始化趋势图表
  const initTrendChart = () => {
    if (trendChart.value) {
      chartInstance = echarts.init(trendChart.value);

      const option = {
        backgroundColor: 'transparent',
        grid: {
          left: '5%',
          right: '5%',
          top: '10%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
          axisLine: {
            lineStyle: {
              color: 'rgba(74, 172, 254, 0.2)'
            }
          },
          axisLabel: {
            color: 'rgba(255, 255, 255, 0.6)',
            fontSize: 11
          },
          axisTick: { show: false }
        },
        yAxis: {
          type: 'value',
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            color: 'rgba(255, 255, 255, 0.6)',
            fontSize: 11
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(74, 172, 254, 0.1)'
            }
          }
        },
        series: [
          {
            name: '播放成功率',
            type: 'line',
            data: [95.2, 96.8, 98.1, 97.5, 98.9, 99.2, 98.5],
            smooth: true,
            lineStyle: {
              color: '#00d4ff',
              width: 3,
              shadowColor: 'rgba(0, 212, 255, 0.3)',
              shadowBlur: 10
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(0, 212, 255, 0.3)' },
                  { offset: 1, color: 'rgba(0, 212, 255, 0.05)' }
                ]
              }
            },
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#00d4ff',
              borderColor: '#ffffff',
              borderWidth: 2
            }
          },
          {
            name: '用户互动率',
            type: 'line',
            data: [12.3, 14.5, 16.8, 15.2, 18.9, 17.1, 15.6],
            smooth: true,
            lineStyle: {
              color: '#ff9f43',
              width: 3,
              shadowColor: 'rgba(255, 159, 67, 0.3)',
              shadowBlur: 10
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(255, 159, 67, 0.3)' },
                  { offset: 1, color: 'rgba(255, 159, 67, 0.05)' }
                ]
              }
            },
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#ff9f43',
              borderColor: '#ffffff',
              borderWidth: 2
            }
          }
        ],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 20, 60, 0.9)',
          borderColor: '#00d4ff',
          borderWidth: 1,
          textStyle: { color: '#ffffff' }
        }
      };

      chartInstance.setOption(option);
    }
  };

  onMounted(() => {
    initTrendChart();
    updateTime();
    timeInterval = setInterval(updateTime, 1000);

    // 窗口大小变化时重新调整图表
    const resizeHandler = () => {
      if (chartInstance) {
        chartInstance.resize();
      }
    };
    window.addEventListener('resize', resizeHandler);
  });

  onUnmounted(() => {
    if (chartInstance) {
      chartInstance.dispose();
    }
    if (timeInterval) {
      clearInterval(timeInterval);
    }
    window.removeEventListener('resize', resizeHandler);
  });

  defineOptions({ name: 'ContentAnalysisCard' });
</script>

<style scoped>
  .content-analysis-card {
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(26, 29, 74, 0.9) 0%,
      rgba(45, 53, 97, 0.8) 100%
    );
    border: 1px solid rgba(74, 172, 254, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
  }

  .card-header-custom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .header-icon-wrapper {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #00d4ff, #4facfe);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
  }

  .header-icon {
    font-size: 16px;
    color: white;
  }

  .header-title {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .header-right .status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
  }

  .status-dot {
    width: 6px;
    height: 6px;
    background: #00ff88;
    border-radius: 50%;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  .analysis-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: calc(100% - 60px);
  }

  /* 核心指标网格 */
  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }

  .metric-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(74, 172, 254, 0.1);
    border-radius: 12px;
    padding: 16px;
    position: relative;
    overflow: hidden;
  }

  .metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #00d4ff, #4facfe, #00ff88);
  }

  .metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .metric-icon {
    width: 28px;
    height: 28px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
  }

  .metric-icon.icon-success {
    background: linear-gradient(135deg, #00d4ff, #4facfe);
  }

  .metric-icon.icon-duration {
    background: linear-gradient(135deg, #ff9f43, #ff6b6b);
  }

  .metric-icon.icon-coverage {
    background: linear-gradient(135deg, #00d4aa, #00ff88);
  }

  .metric-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    font-weight: 500;
  }

  .metric-trend.positive {
    color: #00ff88;
  }

  .metric-trend.negative {
    color: #ff6b6b;
  }

  .metric-body {
    text-align: center;
  }

  .metric-value {
    font-size: 20px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 4px;
  }

  .metric-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 8px;
  }

  .metric-progress {
    width: 100%;
  }

  .progress-track {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    border-radius: 2px;
    transition: width 0.3s ease;
  }

  /* 图表区域 */
  .chart-section {
    flex: 1;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(74, 172, 254, 0.1);
    border-radius: 12px;
    padding: 16px;
  }

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .chart-header h4 {
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
  }

  .chart-legend {
    display: flex;
    gap: 16px;
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
  }

  .legend-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  .legend-dot.success {
    background: #00d4ff;
  }

  .legend-dot.engagement {
    background: #ff9f43;
  }

  .trend-chart {
    width: 100%;
    height: 200px;
  }

  /* 实时数据 */
  .realtime-stats {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(74, 172, 254, 0.1);
    border-radius: 12px;
    padding: 16px;
  }

  .stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .stats-header h4 {
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
  }

  .refresh-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
  }

  .rotating {
    animation: rotate 2s linear infinite;
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(74, 172, 254, 0.1);
  }

  .stat-icon {
    width: 24px;
    height: 24px;
    color: #00d4ff;
    font-size: 14px;
  }

  .stat-content {
    flex: 1;
  }

  .stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    line-height: 1;
  }

  .stat-label {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 2px;
  }

  .stat-change {
    font-size: 11px;
    font-weight: 500;
  }

  .stat-change.positive {
    color: #00ff88;
  }

  .stat-change.negative {
    color: #ff6b6b;
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .metrics-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .metric-card {
      padding: 12px;
    }

    .stats-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  }
</style>
