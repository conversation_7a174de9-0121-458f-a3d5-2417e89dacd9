<template>
  <ele-card class="device-map-card">
    <template #header>
      <div class="card-header-custom">
        <div class="header-left">
          <div class="header-icon-wrapper">
            <el-icon class="header-icon"><Location /></el-icon>
          </div>
          <span class="header-title">区域设备分布</span>
        </div>
        <div class="header-right">
          <div class="header-stats">
            <div class="stat-item">
              <span class="stat-label">总设备</span>
              <span class="stat-value">{{ totalDevices }}</span>
            </div>
            <div class="stat-divider">|</div>
            <div class="stat-item">
              <span class="stat-label">在线率</span>
              <span class="stat-value online">{{ onlineRate }}%</span>
            </div>
          </div>
        </div>
      </div>
    </template>

    <div class="map-content">
      <!-- 地图区域 -->
      <div class="map-container">
        <v-chart
          ref="deviceMapChartRef"
          :option="deviceMapOption"
          class="device-map-chart"
          @click="handleDeviceMapClick"
        />

        <!-- 地图控制面板 -->
        <div class="map-controls">
          <!-- 数据类型切换 -->
          <div class="map-type-controls">
            <el-button-group size="small">
              <el-button
                :type="mapType === 'total' ? 'primary' : ''"
                @click="switchMapType('total')"
              >
                总设备
              </el-button>
              <el-button
                :type="mapType === 'online' ? 'primary' : ''"
                @click="switchMapType('online')"
              >
                在线设备
              </el-button>
              <el-button
                :type="mapType === 'publishing' ? 'primary' : ''"
                @click="switchMapType('publishing')"
              >
                发布中
              </el-button>
            </el-button-group>
          </div>
        </div>
      </div>

      <!-- 区域设备排行 -->
      <div class="device-ranking">
        <div class="ranking-header">
          <h4>区域设备排行</h4>
          <el-button text size="small" @click="refreshData">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>

        <!-- 排行榜列表 -->
        <div class="ranking-list">
          <div
            v-for="(item, index) in deviceRankingData.slice(0, 3)"
            :key="item.name"
            class="ranking-item"
            :class="`rank-${index + 1}`"
          >
            <div class="rank-badge">
              <div class="badge-icon" :class="getRankClass(index)">
                <span class="rank-text">{{ index + 1 }}</span>
              </div>
            </div>
            <div class="device-info">
              <div class="device-name">{{ item.name }}</div>
              <div class="device-stats">
                <span class="total-devices"
                  >{{ item.total.toLocaleString() }}台</span
                >
                <span class="divider">|</span>
                <span class="online-rate success"
                  >{{ item.onlineRate }}%在线</span
                >
              </div>
            </div>
            <div class="rank-decoration"></div>
          </div>
        </div>
      </div>
    </div>
  </ele-card>
</template>

<script setup>
  import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { use, registerMap } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { MapChart, ScatterChart, EffectScatterChart } from 'echarts/charts';
  import {
    TooltipComponent,
    VisualMapComponent,
    GeoComponent
  } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { Location, Refresh } from '@element-plus/icons-vue';
  import * as echarts from 'echarts';

  use([
    CanvasRenderer,
    MapChart,
    ScatterChart,
    EffectScatterChart,
    TooltipComponent,
    VisualMapComponent,
    GeoComponent
  ]);

  // 地图引用
  const deviceMapChartRef = ref();

  // 地图类型
  const mapType = ref('total');

  // 地图是否已加载
  const mapLoaded = ref(false);

  // 模拟设备数据
  const deviceData = reactive({
    total: [
      { name: '北京', value: [116.46, 39.92, 1250] },
      { name: '上海', value: [121.48, 31.22, 980] },
      { name: '深圳', value: [114.07, 22.62, 856] },
      { name: '广州', value: [113.23, 23.16, 743] },
      { name: '杭州', value: [120.19, 30.26, 632] },
      { name: '成都', value: [104.06, 30.67, 578] },
      { name: '重庆', value: [106.54, 29.59, 523] },
      { name: '武汉', value: [114.31, 30.52, 467] },
      { name: '西安', value: [108.95, 34.27, 412] },
      { name: '南京', value: [118.78, 32.04, 389] }
    ],
    online: [
      { name: '北京', value: [116.46, 39.92, 1156] },
      { name: '上海', value: [121.48, 31.22, 901] },
      { name: '深圳', value: [114.07, 22.62, 798] },
      { name: '广州', value: [113.23, 23.16, 687] },
      { name: '杭州', value: [120.19, 30.26, 589] },
      { name: '成都', value: [104.06, 30.67, 534] },
      { name: '重庆', value: [106.54, 29.59, 478] },
      { name: '武汉', value: [114.31, 30.52, 423] },
      { name: '西安', value: [108.95, 34.27, 378] },
      { name: '南京', value: [118.78, 32.04, 356] }
    ],
    publishing: [
      { name: '北京', value: [116.46, 39.92, 892] },
      { name: '上海', value: [121.48, 31.22, 734] },
      { name: '深圳', value: [114.07, 22.62, 645] },
      { name: '广州', value: [113.23, 23.16, 567] },
      { name: '杭州', value: [120.19, 30.26, 478] },
      { name: '成都', value: [104.06, 30.67, 423] },
      { name: '重庆', value: [106.54, 29.59, 389] },
      { name: '武汉', value: [114.31, 30.52, 334] },
      { name: '西安', value: [108.95, 34.27, 298] },
      { name: '南京', value: [118.78, 32.04, 267] }
    ]
  });

  // 计算属性
  const totalDevices = computed(() => {
    return deviceData.total
      .reduce((sum, item) => sum + item.value[2], 0)
      .toLocaleString();
  });

  const onlineRate = computed(() => {
    const total = deviceData.total.reduce(
      (sum, item) => sum + item.value[2],
      0
    );
    const online = deviceData.online.reduce(
      (sum, item) => sum + item.value[2],
      0
    );
    return Math.round((online / total) * 100);
  });

  // 排行榜数据
  const deviceRankingData = computed(() => {
    const currentData = deviceData[mapType.value];
    const maxValue = Math.max(...currentData.map((item) => item.value[2]));

    return currentData
      .sort((a, b) => b.value[2] - a.value[2])
      .slice(0, 8)
      .map((item) => ({
        name: item.name,
        total: item.value[2],
        online: Math.round(item.value[2] * 0.92),
        onlineRate: Math.round(92 + Math.random() * 6)
      }));
  });

  // 省份数据映射
  const provinceDataMap = computed(() => {
    const currentData = deviceData[mapType.value];
    const map = new Map();

    // 城市到省份的映射
    const cityToProvince = {
      北京: '北京',
      上海: '上海',
      天津: '天津',
      重庆: '重庆',
      深圳: '广东',
      广州: '广东',
      杭州: '浙江',
      成都: '四川',
      武汉: '湖北',
      西安: '陕西',
      南京: '江苏',
      苏州: '江苏',
      青岛: '山东',
      大连: '辽宁',
      宁波: '浙江',
      厦门: '福建'
    };

    // 聚合省份数据
    currentData.forEach((city) => {
      const province = cityToProvince[city.name] || city.name;
      const currentValue = map.get(province) || 0;
      map.set(province, currentValue + city.value[2]);
    });

    return map;
  });

  // 地图配置
  const deviceMapOption = computed(() => {
    if (!mapLoaded.value) {
      return {
        backgroundColor: 'transparent',
        graphic: {
          type: 'text',
          left: 'center',
          top: 'center',
          style: {
            text: '地图加载中...',
            fontSize: 16,
            fill: '#666666'
          }
        }
      };
    }

    const provinceData = provinceDataMap.value;
    const maxValue = Math.max(...Array.from(provinceData.values()));
    const minValue = Math.min(...Array.from(provinceData.values()));

    return {
      backgroundColor: 'transparent',
      visualMap: {
        min: minValue,
        max: maxValue,
        left: 'right',
        top: 'middle',
        orient: 'vertical',
        text: ['设备多', '设备少'],
        textGap: 10,
        calculable: false,
        realtime: true,
        inRange: {
          color: ['#e3f2fd', '#90caf9', '#42a5f5', '#1976d2', '#0d47a1']
        },
        textStyle: {
          color: '#374151',
          fontSize: 11,
          fontWeight: 500
        },
        itemWidth: 12,
        itemHeight: 80,
        seriesIndex: [0],
        backgroundColor: '#ffffff',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        borderRadius: 6,
        padding: 8,
        shadowBlur: 6,
        shadowColor: 'rgba(0, 0, 0, 0.1)',
        shadowOffsetX: 0,
        shadowOffsetY: 2,
        right: 20
      },
      tooltip: {
        trigger: 'item',
        formatter: (params) => {
          const provinceName = params.name;
          const deviceCount = provinceData.get(provinceName) || 0;
          const onlineRate = Math.round(88 + Math.random() * 10);
          return `${provinceName}<br/>设备数量: ${deviceCount.toLocaleString()}<br/>在线率: ${onlineRate}%`;
        },
        backgroundColor: '#ffffff',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        textStyle: {
          color: '#374151',
          fontSize: 12
        }
      },
      series: [
        {
          name: '设备数量',
          type: 'map',
          map: 'china',
          roam: false,
          zoom: 1.2,
          center: [105, 36],
          aspectScale: 0.75,
          data: Array.from(provinceData.entries()).map(([name, value]) => ({
            name,
            value
          })),
          label: {
            show: true,
            fontSize: 13,
            color: '#1f2937',
            fontWeight: '600',
            textBorderColor: '#ffffff',
            textBorderWidth: 2
          },
          itemStyle: {
            borderColor: '#ffffff',
            borderWidth: 1.5,
            shadowBlur: 4,
            shadowColor: 'rgba(0, 0, 0, 0.1)',
            shadowOffsetX: 0,
            shadowOffsetY: 2
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 15,
              color: '#111827',
              fontWeight: 'bold',
              textBorderColor: '#ffffff',
              textBorderWidth: 3
            },
            itemStyle: {
              borderColor: '#f59e0b',
              borderWidth: 2.5,
              shadowBlur: 12,
              shadowColor: 'rgba(245, 158, 11, 0.4)',
              shadowOffsetX: 0,
              shadowOffsetY: 4
            }
          }
        }
      ]
    };
  });

  // 方法
  const switchMapType = (type) => {
    mapType.value = type;
    EleMessage.success(
      `切换到${type === 'total' ? '总设备' : type === 'online' ? '在线设备' : '发布中设备'}视图`
    );
  };

  const handleDeviceMapClick = (params) => {
    if (params.componentType === 'geo') {
      const provinceName = params.name;
      const deviceCount = provinceDataMap.value.get(provinceName) || 0;
      EleMessage.info(
        `选中省份: ${provinceName}，设备数量: ${deviceCount.toLocaleString()}`
      );
    }
  };

  const refreshData = () => {
    EleMessage.success('数据已刷新');
  };

  const getOnlineStatusClass = (rate) => {
    if (rate >= 95) return 'excellent';
    if (rate >= 85) return 'good';
    if (rate >= 70) return 'normal';
    return 'poor';
  };

  const getRankClass = (index) => {
    switch (index) {
      case 0:
        return 'rank-first';
      case 1:
        return 'rank-second';
      case 2:
        return 'rank-third';
      default:
        return 'rank-other';
    }
  };

  // 创建简单的中国地图数据
  const createSimpleMapData = () => {
    return {
      type: 'FeatureCollection',
      features: [
        {
          type: 'Feature',
          properties: { name: '北京' },
          geometry: {
            type: 'Polygon',
            coordinates: [
              [
                [116, 40.2],
                [117, 40.2],
                [117, 39.5],
                [116, 39.5],
                [116, 40.2]
              ]
            ]
          }
        },
        {
          type: 'Feature',
          properties: { name: '上海' },
          geometry: {
            type: 'Polygon',
            coordinates: [
              [
                [121, 31.5],
                [122, 31.5],
                [122, 30.8],
                [121, 30.8],
                [121, 31.5]
              ]
            ]
          }
        },
        {
          type: 'Feature',
          properties: { name: '广东' },
          geometry: {
            type: 'Polygon',
            coordinates: [
              [
                [113, 24],
                [116, 24],
                [116, 20],
                [113, 20],
                [113, 24]
              ]
            ]
          }
        },
        {
          type: 'Feature',
          properties: { name: '浙江' },
          geometry: {
            type: 'Polygon',
            coordinates: [
              [
                [119, 31],
                [122, 31],
                [122, 27],
                [119, 27],
                [119, 31]
              ]
            ]
          }
        },
        {
          type: 'Feature',
          properties: { name: '江苏' },
          geometry: {
            type: 'Polygon',
            coordinates: [
              [
                [117, 35],
                [122, 35],
                [122, 31],
                [117, 31],
                [117, 35]
              ]
            ]
          }
        },
        {
          type: 'Feature',
          properties: { name: '四川' },
          geometry: {
            type: 'Polygon',
            coordinates: [
              [
                [102, 34],
                [110, 34],
                [110, 26],
                [102, 26],
                [102, 34]
              ]
            ]
          }
        }
      ]
    };
  };

  // 生命周期
  onMounted(async () => {
    try {
      // 加载真正的中国地图数据
      const response = await fetch('/json/china-provinces.geo.json');
      if (!response.ok) {
        throw new Error('地图数据加载失败');
      }
      const mapData = await response.json();
      registerMap('china', mapData);
      mapLoaded.value = true;
      console.log('地图数据加载成功');
    } catch (error) {
      console.error('地图数据加载失败:', error);
      // 如果加载失败，使用简单的备用数据
      const fallbackMapData = createSimpleMapData();
      registerMap('china', fallbackMapData);
      mapLoaded.value = true;
    }
  });

  defineOptions({ name: 'DeviceMapCard' });
</script>

<style scoped>
  .device-map-card {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 100%
    );
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.1),
      0 0 20px rgba(74, 144, 226, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    overflow: hidden;
  }

  .card-header-custom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .header-icon-wrapper {
    width: 32px;
    height: 32px;
    background: var(--el-color-primary);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .header-icon {
    font-size: 16px;
    color: white;
  }

  .header-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .header-right .header-stats {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .stat-label {
    font-size: 12px;
    color: var(--el-text-color-regular);
  }

  .stat-value {
    font-size: 14px;
    font-weight: 700;
    color: var(--el-text-color-primary);
  }

  .stat-value.online {
    color: var(--el-color-success);
  }

  .stat-divider {
    color: var(--el-border-color);
    font-size: 12px;
    margin: 0 4px;
  }

  .map-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
  }

  /* 地图容器样式 */
  .map-container {
    height: 450px;
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid rgba(148, 163, 184, 0.2);
    box-shadow:
      0 10px 25px rgba(0, 0, 0, 0.08),
      0 4px 10px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .map-container:hover {
    box-shadow:
      0 15px 35px rgba(0, 0, 0, 0.12),
      0 6px 15px rgba(0, 0, 0, 0.06),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }

  /* 暗色主题适配 */
  :global(.dark) .map-container {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    border-color: rgba(71, 85, 105, 0.3);
    box-shadow:
      0 10px 25px rgba(0, 0, 0, 0.3),
      0 4px 10px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
  }

  :global(.dark) .map-container:hover {
    box-shadow:
      0 15px 35px rgba(0, 0, 0, 0.4),
      0 6px 15px rgba(0, 0, 0, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.08);
  }

  .device-map-chart {
    width: 100%;
    height: 100%;
  }

  /* 地图控制按钮 */
  .map-controls {
    position: absolute;
    top: 16px;
    left: 16px;
    z-index: 10;
  }

  .map-type-controls {
    flex-shrink: 0;
  }

  .map-controls :deep(.el-button-group) {
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.1),
      0 2px 4px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    overflow: hidden;
    background: #ffffff;
    border: 1px solid #e5e7eb;
  }

  .map-controls :deep(.el-button) {
    background: #ffffff;
    border: none;
    color: #374151;
    font-weight: 500;
    transition: all 0.2s ease;
    position: relative;
  }

  .map-controls :deep(.el-button:not(:last-child)) {
    border-right: 1px solid #e5e7eb;
  }

  .map-controls :deep(.el-button:hover) {
    background: #f3f4f6;
    color: #1f2937;
  }

  .map-controls :deep(.el-button--primary) {
    background: #3b82f6;
    color: #ffffff;
    font-weight: 600;
  }

  .map-controls :deep(.el-button--primary:hover) {
    background: #2563eb;
  }

  /* 暗色主题适配 */
  :global(.dark) .map-controls :deep(.el-button-group) {
    background: #374151;
    border-color: #4b5563;
  }

  :global(.dark) .map-controls :deep(.el-button) {
    background: #374151;
    color: #d1d5db;
  }

  :global(.dark) .map-controls :deep(.el-button:not(:last-child)) {
    border-right-color: #4b5563;
  }

  :global(.dark) .map-controls :deep(.el-button:hover) {
    background: #4b5563;
    color: #f9fafb;
  }

  :global(.dark) .map-controls :deep(.el-button--primary) {
    background: #3b82f6;
    color: #ffffff;
  }

  :global(.dark) .map-controls :deep(.el-button--primary:hover) {
    background: #2563eb;
  }

  .device-ranking {
    display: flex;
    flex-direction: column;
    gap: 8px;
    height: 280px;
    overflow-y: auto;
    padding: 16px 0;
  }

  .ranking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .ranking-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }

  .ranking-header :deep(.el-button) {
    color: #4facfe;
    background: transparent;
    border: none;
    padding: 4px;
  }

  .ranking-header :deep(.el-button:hover) {
    background: rgba(74, 172, 254, 0.1);
    color: #00d4ff;
  }

  .ranking-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 6px;
    margin-bottom: 10px;
  }

  .ranking-list::-webkit-scrollbar {
    width: 4px;
  }

  .ranking-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  .ranking-list::-webkit-scrollbar-thumb {
    background: rgba(74, 172, 254, 0.5);
    border-radius: 2px;
  }

  .ranking-item {
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .ranking-item:hover {
    transform: translateY(-2px);
    background: #f9fafb;
    border-color: #d1d5db;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  }

  .ranking-item.rank-1 {
    border-color: #fbbf24;
    background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
  }

  .ranking-item.rank-1:hover {
    border-color: #f59e0b;
    box-shadow: 0 8px 20px rgba(251, 191, 36, 0.2);
  }

  .ranking-item.rank-2 {
    border-color: #9ca3af;
    background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
  }

  .ranking-item.rank-2:hover {
    border-color: #6b7280;
    box-shadow: 0 8px 20px rgba(156, 163, 175, 0.2);
  }

  .ranking-item.rank-3 {
    border-color: #d97706;
    background: linear-gradient(135deg, #fef3e2 0%, #ffffff 100%);
  }

  .ranking-item.rank-3:hover {
    border-color: #b45309;
    box-shadow: 0 8px 20px rgba(217, 119, 6, 0.2);
  }

  /* 暗色主题适配 */
  :global(.dark) .ranking-item {
    background: #374151;
    border-color: #4b5563;
  }

  :global(.dark) .ranking-item:hover {
    background: #4b5563;
    border-color: #6b7280;
  }

  :global(.dark) .ranking-item.rank-1 {
    background: linear-gradient(135deg, #451a03 0%, #374151 100%);
    border-color: #fbbf24;
  }

  :global(.dark) .ranking-item.rank-2 {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border-color: #9ca3af;
  }

  :global(.dark) .ranking-item.rank-3 {
    background: linear-gradient(135deg, #431407 0%, #374151 100%);
    border-color: #d97706;
  }

  .rank-badge {
    position: relative;
    z-index: 2;
    flex-shrink: 0;
  }

  .badge-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
  }

  .badge-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.3) 0%,
      transparent 50%,
      rgba(0, 0, 0, 0.1) 100%
    );
    border-radius: 50%;
  }

  .badge-icon.rank-first {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #b8860b;
    box-shadow:
      0 6px 20px rgba(255, 215, 0, 0.4),
      inset 0 2px 0 rgba(255, 255, 255, 0.4);
  }

  .badge-icon.rank-second {
    background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
    color: #696969;
    box-shadow:
      0 6px 20px rgba(192, 192, 192, 0.4),
      inset 0 2px 0 rgba(255, 255, 255, 0.4);
  }

  .badge-icon.rank-third {
    background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
    color: #8b4513;
    box-shadow:
      0 6px 20px rgba(205, 127, 50, 0.4),
      inset 0 2px 0 rgba(255, 255, 255, 0.4);
  }

  .ranking-item:hover .badge-icon {
    transform: scale(1.05);
  }

  .rank-text {
    font-size: 14px;
    font-weight: 800;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 1;
  }

  .device-info {
    flex: 1;
    position: relative;
    z-index: 2;
  }

  .device-name {
    font-size: 14px;
    font-weight: 700;
    color: var(--el-text-color-primary);
    margin-bottom: 2px;
  }

  .device-stats {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
  }

  .total-devices {
    color: var(--el-text-color-regular);
    font-weight: 600;
  }

  .online-rate {
    font-weight: 600;
  }

  .online-rate.success {
    color: var(--el-color-success);
  }

  .rank-decoration {
    position: absolute;
    top: 0;
    right: 0;
    width: 40px;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent 0%,
      var(--el-fill-color-extra-light) 100%
    );
    opacity: 0.3;
  }

  .online-status {
    font-size: 12px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 4px;
  }

  .online-status.excellent {
    background: var(--el-color-success-light-9);
    color: var(--el-color-success);
  }

  .online-status.good {
    background: var(--el-color-warning-light-9);
    color: var(--el-color-warning);
  }

  .online-status.normal {
    background: var(--el-color-info-light-9);
    color: var(--el-color-info);
  }

  .online-status.poor {
    background: var(--el-color-danger-light-9);
    color: var(--el-color-danger);
  }

  .progress-indicator {
    flex-shrink: 0;
  }

  .progress-circle {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid;
    position: relative;
  }

  .progress-circle.primary {
    border-color: var(--el-color-primary-light-7);
    color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
  }

  .progress-circle.warning {
    border-color: var(--el-color-warning-light-7);
    color: var(--el-color-warning);
    background: var(--el-color-warning-light-9);
  }

  .progress-circle.success {
    border-color: var(--el-color-success-light-7);
    color: var(--el-color-success);
    background: var(--el-color-success-light-9);
  }

  .progress-circle.info {
    border-color: var(--el-color-info-light-7);
    color: var(--el-color-info);
    background: var(--el-color-info-light-9);
  }

  .progress-circle.danger {
    border-color: var(--el-color-danger-light-7);
    color: var(--el-color-danger);
    background: var(--el-color-danger-light-9);
  }

  .progress-value {
    font-size: 11px;
    font-weight: 700;
  }

  .progress-container {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
  }

  .progress-bar {
    width: 60px;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
  }

  .progress-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.8s ease;
    position: relative;
  }

  .progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
    );
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  .percent-text {
    font-size: 12px;
    font-weight: 600;
    color: #4facfe;
    min-width: 35px;
    text-align: right;
  }

  .divider {
    color: var(--el-border-color);
    margin: 0 4px;
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .header-stats {
      gap: 16px;
    }

    .map-container,
    .device-ranking {
      height: 400px;
    }

    .map-legend {
      bottom: 8px;
      left: 8px;
      padding: 8px;
    }

    .legend-items {
      gap: 3px;
    }
  }

  @media (max-width: 768px) {
    .card-header-custom {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
    }

    .header-stats {
      align-self: stretch;
      justify-content: space-between;
    }

    .map-container,
    .device-ranking {
      height: 300px;
    }

    .map-controls {
      top: 8px;
      left: 8px;
    }

    .map-controls :deep(.el-button) {
      padding: 4px 8px;
      font-size: 12px;
    }

    .ranking-item {
      padding: 8px;
      gap: 8px;
    }

    .rank-number {
      width: 28px;
      height: 28px;
      font-size: 12px;
    }

    .region-name {
      font-size: 13px;
    }

    .region-stats {
      font-size: 11px;
      gap: 8px;
    }

    .progress-bar {
      width: 50px;
      height: 4px;
    }

    .percent-text {
      font-size: 11px;
      min-width: 30px;
    }
  }

  @media (max-width: 480px) {
    .header-title {
      font-size: 16px;
    }

    .stat-value {
      font-size: 14px;
    }

    .map-container,
    .device-ranking {
      height: 250px;
    }

    .ranking-item {
      padding: 6px;
    }

    .rank-number {
      width: 24px;
      height: 24px;
      font-size: 11px;
    }

    .region-name {
      font-size: 12px;
    }

    .region-stats {
      font-size: 10px;
    }

    .progress-bar {
      width: 40px;
      height: 3px;
    }

    .percent-text {
      font-size: 10px;
      min-width: 25px;
    }
  }
</style>
