<template>
  <ele-card class="system-status-card">
    <template #header>
      <div class="card-header-custom">
        <div class="header-left">
          <div class="header-icon-wrapper">
            <el-icon class="header-icon"><Monitor /></el-icon>
          </div>
          <span class="header-title">系统状态监控</span>
        </div>
        <div class="header-right">
          <div
            class="system-status"
            :class="
              systemHealth > 95
                ? 'healthy'
                : systemHealth > 80
                  ? 'warning'
                  : 'critical'
            "
          >
            <div class="status-dot"></div>
            <span>{{
              systemHealth > 95
                ? '运行正常'
                : systemHealth > 80
                  ? '轻微异常'
                  : '需要关注'
            }}</span>
          </div>
        </div>
      </div>
    </template>

    <div class="status-content">
      <!-- 系统健康度仪表盘 -->
      <div class="health-dashboard">
        <div class="health-gauge">
          <div class="gauge-container">
            <canvas ref="gaugeCanvas" width="120" height="120"></canvas>
            <div class="gauge-center">
              <div class="health-score">{{ systemHealth }}</div>
              <div class="health-label">健康度</div>
            </div>
          </div>
        </div>
        <div class="health-info">
          <div class="info-item">
            <el-icon><Clock /></el-icon>
            <span>运行时间: {{ uptime }}</span>
          </div>
          <div class="info-item">
            <el-icon><Refresh /></el-icon>
            <span>最后更新: {{ lastUpdateTime }}</span>
          </div>
        </div>
      </div>

      <!-- 系统资源监控 -->
      <div class="resource-monitor">
        <div class="monitor-header">
          <h4>资源使用情况</h4>
          <div class="monitor-controls">
            <el-icon class="refresh-btn" @click="refreshData"
              ><Refresh
            /></el-icon>
          </div>
        </div>

        <div class="resource-grid">
          <div
            class="resource-item"
            v-for="(resource, index) in systemResources"
            :key="index"
          >
            <div class="resource-header">
              <div class="resource-icon" :class="`icon-${resource.type}`">
                <component :is="resource.icon" />
              </div>
              <div class="resource-info">
                <div class="resource-name">{{ resource.name }}</div>
                <div class="resource-value">{{ resource.value }}%</div>
              </div>
            </div>

            <div class="resource-progress">
              <div class="progress-track">
                <div
                  class="progress-fill"
                  :class="`fill-${resource.type}`"
                  :style="{ width: resource.value + '%' }"
                ></div>
                <div
                  class="progress-threshold"
                  v-if="resource.threshold"
                  :style="{ left: resource.threshold + '%' }"
                ></div>
              </div>
              <div class="progress-labels">
                <span class="label-min">0%</span>
                <span class="label-max">100%</span>
              </div>
            </div>

            <div
              class="resource-status"
              :class="getResourceStatus(resource.value, resource.threshold)"
            >
              <el-icon
                ><component
                  :is="getStatusIcon(resource.value, resource.threshold)"
              /></el-icon>
              <span>{{
                getStatusText(resource.value, resource.threshold)
              }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统警告和通知 -->
      <div class="system-alerts" v-if="alerts.length > 0">
        <div class="alerts-header">
          <h4>系统警告</h4>
          <div class="alert-count">{{ alerts.length }}</div>
        </div>
        <div class="alerts-list">
          <div
            class="alert-item"
            v-for="(alert, index) in alerts"
            :key="index"
            :class="`alert-${alert.level}`"
          >
            <div class="alert-icon">
              <el-icon><component :is="alert.icon" /></el-icon>
            </div>
            <div class="alert-content">
              <div class="alert-message">{{ alert.message }}</div>
              <div class="alert-time">{{ alert.time }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ele-card>
</template>

<script setup>
  import { ref, reactive, onMounted, onUnmounted } from 'vue';
  import {
    Monitor,
    Cpu,
    Coin,
    Folder,
    Connection,
    Refresh,
    Clock,
    CircleCheck,
    Warning,
    CircleClose,
    InfoFilled
  } from '@element-plus/icons-vue';

  const gaugeCanvas = ref(null);
  const lastUpdateTime = ref('');
  const uptime = ref('');
  let updateInterval = null;

  // 系统健康度
  const systemHealth = ref(98.5);

  // 系统资源数据
  const systemResources = reactive([
    {
      type: 'cpu',
      name: 'CPU使用率',
      value: 35,
      threshold: 80,
      icon: Cpu
    },
    {
      type: 'memory',
      name: '内存使用率',
      value: 68,
      threshold: 85,
      icon: Coin
    },
    {
      type: 'disk',
      name: '磁盘使用率',
      value: 42,
      threshold: 90,
      icon: Folder
    },
    {
      type: 'network',
      name: '网络流量',
      value: 28,
      threshold: 75,
      icon: Connection
    }
  ]);

  // 系统警告
  const alerts = reactive([
    {
      level: 'warning',
      message: '内存使用率较高，建议清理缓存',
      time: '2分钟前',
      icon: Warning
    }
  ]);

  // 绘制健康度仪表盘
  const drawHealthGauge = () => {
    const canvas = gaugeCanvas.value;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const centerX = 60;
    const centerY = 60;
    const radius = 45;

    // 清空画布
    ctx.clearRect(0, 0, 120, 120);

    // 绘制背景圆弧
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0.75 * Math.PI, 2.25 * Math.PI);
    ctx.lineWidth = 8;
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineCap = 'round';
    ctx.stroke();

    // 绘制进度圆弧
    const progress = systemHealth.value / 100;
    const endAngle = 0.75 * Math.PI + progress * 1.5 * Math.PI;

    // 创建渐变
    const gradient = ctx.createLinearGradient(0, 0, 120, 120);
    if (systemHealth.value > 95) {
      gradient.addColorStop(0, '#00ff88');
      gradient.addColorStop(1, '#00d4aa');
    } else if (systemHealth.value > 80) {
      gradient.addColorStop(0, '#ffd93d');
      gradient.addColorStop(1, '#ff9f43');
    } else {
      gradient.addColorStop(0, '#ff6b6b');
      gradient.addColorStop(1, '#ff4757');
    }

    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0.75 * Math.PI, endAngle);
    ctx.lineWidth = 8;
    ctx.strokeStyle = gradient;
    ctx.lineCap = 'round';
    ctx.stroke();

    // 绘制指示点
    const dotX = centerX + Math.cos(endAngle - Math.PI / 2) * radius;
    const dotY = centerY + Math.sin(endAngle - Math.PI / 2) * radius;

    ctx.beginPath();
    ctx.arc(dotX, dotY, 4, 0, 2 * Math.PI);
    ctx.fillStyle = '#ffffff';
    ctx.fill();
  };

  // 获取资源状态
  const getResourceStatus = (value, threshold) => {
    if (value >= threshold) return 'critical';
    if (value >= threshold * 0.8) return 'warning';
    return 'normal';
  };

  // 获取状态图标
  const getStatusIcon = (value, threshold) => {
    if (value >= threshold) return CircleClose;
    if (value >= threshold * 0.8) return Warning;
    return CircleCheck;
  };

  // 获取状态文本
  const getStatusText = (value, threshold) => {
    if (value >= threshold) return '超出阈值';
    if (value >= threshold * 0.8) return '接近阈值';
    return '正常';
  };

  // 刷新数据
  const refreshData = () => {
    // 模拟数据更新
    systemResources.forEach((resource) => {
      resource.value = Math.max(
        10,
        Math.min(95, resource.value + (Math.random() - 0.5) * 10)
      );
    });

    // 更新健康度
    const avgUsage =
      systemResources.reduce((sum, r) => sum + r.value, 0) /
      systemResources.length;
    systemHealth.value = Math.round(100 - avgUsage * 0.3);

    updateTime();
    drawHealthGauge();
  };

  // 更新时间
  const updateTime = () => {
    const now = new Date();
    lastUpdateTime.value = now.toLocaleTimeString();

    // 计算运行时间（模拟）
    const hours = Math.floor(Math.random() * 24) + 720; // 30天+随机小时
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    uptime.value = `${days}天${remainingHours}小时`;
  };

  onMounted(() => {
    updateTime();
    drawHealthGauge();

    // 定期更新数据
    updateInterval = setInterval(() => {
      refreshData();
    }, 30000); // 30秒更新一次
  });

  onUnmounted(() => {
    if (updateInterval) {
      clearInterval(updateInterval);
    }
  });

  defineOptions({ name: 'SystemStatusCard' });
</script>

<style scoped>
  .system-status-card {
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(26, 29, 74, 0.9) 0%,
      rgba(45, 53, 97, 0.8) 100%
    );
    border: 1px solid rgba(74, 172, 254, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
  }

  .card-header-custom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .header-icon-wrapper {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #00d4ff, #4facfe);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
  }

  .header-icon {
    font-size: 16px;
    color: white;
  }

  .header-title {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .header-right .system-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    font-weight: 500;
  }

  .system-status.healthy {
    color: #00ff88;
  }

  .system-status.warning {
    color: #ffd93d;
  }

  .system-status.critical {
    color: #ff6b6b;
  }

  .status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  .status-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: calc(100% - 60px);
  }

  /* 健康度仪表盘 */
  .health-dashboard {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(74, 172, 254, 0.1);
    border-radius: 12px;
  }

  .health-gauge {
    position: relative;
  }

  .gauge-container {
    position: relative;
    width: 120px;
    height: 120px;
  }

  .gauge-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
  }

  .health-score {
    font-size: 24px;
    font-weight: 700;
    color: #ffffff;
    line-height: 1;
  }

  .health-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 4px;
  }

  .health-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
  }

  .info-item .el-icon {
    color: #00d4ff;
    font-size: 16px;
  }

  /* 资源监控 */
  .resource-monitor {
    flex: 1;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(74, 172, 254, 0.1);
    border-radius: 12px;
    padding: 16px;
  }

  .monitor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .monitor-header h4 {
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
  }

  .refresh-btn {
    color: #00d4ff;
    cursor: pointer;
    transition: color 0.3s ease;
  }

  .refresh-btn:hover {
    color: #4facfe;
  }

  .resource-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }

  .resource-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(74, 172, 254, 0.1);
    border-radius: 8px;
    padding: 12px;
  }

  .resource-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
  }

  .resource-icon {
    width: 24px;
    height: 24px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
  }

  .resource-icon.icon-cpu {
    background: linear-gradient(135deg, #ff6b6b, #ff4757);
  }

  .resource-icon.icon-memory {
    background: linear-gradient(135deg, #ffd93d, #ff9f43);
  }

  .resource-icon.icon-disk {
    background: linear-gradient(135deg, #00d4ff, #4facfe);
  }

  .resource-icon.icon-network {
    background: linear-gradient(135deg, #00ff88, #00d4aa);
  }

  .resource-info {
    flex: 1;
  }

  .resource-name {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1;
  }

  .resource-value {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    line-height: 1;
    margin-top: 2px;
  }

  .resource-progress {
    margin: 8px 0;
  }

  .progress-track {
    position: relative;
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    border-radius: 2px;
    transition: width 0.3s ease;
  }

  .progress-fill.fill-cpu {
    background: linear-gradient(90deg, #ff6b6b, #ff4757);
  }

  .progress-fill.fill-memory {
    background: linear-gradient(90deg, #ffd93d, #ff9f43);
  }

  .progress-fill.fill-disk {
    background: linear-gradient(90deg, #00d4ff, #4facfe);
  }

  .progress-fill.fill-network {
    background: linear-gradient(90deg, #00ff88, #00d4aa);
  }

  .progress-threshold {
    position: absolute;
    top: 0;
    width: 2px;
    height: 100%;
    background: #ffffff;
    opacity: 0.6;
  }

  .progress-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 4px;
    font-size: 10px;
    color: rgba(255, 255, 255, 0.5);
  }

  .resource-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    font-weight: 500;
  }

  .resource-status.normal {
    color: #00ff88;
  }

  .resource-status.warning {
    color: #ffd93d;
  }

  .resource-status.critical {
    color: #ff6b6b;
  }

  /* 系统警告 */
  .system-alerts {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 159, 67, 0.2);
    border-radius: 12px;
    padding: 16px;
  }

  .alerts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .alerts-header h4 {
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
  }

  .alert-count {
    background: #ff9f43;
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
  }

  .alerts-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .alert-item {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    padding: 8px;
    border-radius: 6px;
    border-left: 3px solid;
  }

  .alert-item.alert-warning {
    background: rgba(255, 159, 67, 0.1);
    border-left-color: #ff9f43;
  }

  .alert-item.alert-error {
    background: rgba(255, 107, 107, 0.1);
    border-left-color: #ff6b6b;
  }

  .alert-icon {
    color: currentColor;
    font-size: 14px;
    margin-top: 1px;
  }

  .alert-content {
    flex: 1;
  }

  .alert-message {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.4;
  }

  .alert-time {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 2px;
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .health-dashboard {
      flex-direction: column;
      text-align: center;
      gap: 16px;
    }

    .resource-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .resource-item {
      padding: 10px;
    }
  }
</style>
