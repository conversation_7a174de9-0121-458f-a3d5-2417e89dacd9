<template>
  <ele-card class="content-update-card">
    <template #header>
      <div class="card-header-custom">
        <div class="header-left">
          <div class="header-icon-wrapper">
            <el-icon class="header-icon"><TrendCharts /></el-icon>
          </div>
          <span class="header-title">内容更新动态</span>
        </div>
        <div class="header-right">
          <div class="update-frequency">
            <div class="frequency-dot"></div>
            <span>实时更新</span>
          </div>
        </div>
      </div>
    </template>

    <div class="update-content">
      <!-- 更新统计 -->
      <div class="update-stats">
        <div
          class="stat-card"
          v-for="(stat, index) in updateStats"
          :key="index"
        >
          <div class="stat-header">
            <div class="stat-icon" :class="`icon-${stat.type}`">
              <component :is="stat.icon" />
            </div>
            <div
              class="stat-trend"
              :class="stat.trend > 0 ? 'positive' : 'negative'"
            >
              <el-icon
                ><CaretTop v-if="stat.trend > 0" /><CaretBottom v-else
              /></el-icon>
              <span>{{ Math.abs(stat.trend) }}%</span>
            </div>
          </div>
          <div class="stat-body">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </div>

      <!-- 更新频率图表 -->
      <div class="frequency-chart-section">
        <div class="chart-header">
          <h4>24小时更新频率</h4>
          <div class="time-filter">
            <el-button-group size="small">
              <el-button
                v-for="filter in timeFilters"
                :key="filter.value"
                :type="activeTimeFilter === filter.value ? 'primary' : ''"
                @click="activeTimeFilter = filter.value"
                size="small"
              >
                {{ filter.label }}
              </el-button>
            </el-button-group>
          </div>
        </div>
        <div class="frequency-chart" ref="frequencyChart"></div>
      </div>

      <!-- 最新更新列表 -->
      <div class="recent-updates">
        <div class="updates-header">
          <h4>最新更新内容</h4>
          <div class="updates-count">{{ recentUpdates.length }} 条</div>
        </div>
        <div class="updates-list">
          <div
            class="update-item"
            v-for="(update, index) in recentUpdates"
            :key="index"
          >
            <div class="update-indicator">
              <div class="indicator-dot" :class="`dot-${update.type}`"></div>
              <div
                class="indicator-line"
                v-if="index < recentUpdates.length - 1"
              ></div>
            </div>
            <div class="update-content-info">
              <div class="update-title">{{ update.title }}</div>
              <div class="update-meta">
                <span class="update-type" :class="`type-${update.type}`">{{
                  update.typeLabel
                }}</span>
                <span class="update-time">{{ update.time }}</span>
                <span class="update-author">{{ update.author }}</span>
              </div>
            </div>
            <div class="update-status" :class="`status-${update.status}`">
              <el-icon
                ><component :is="getStatusIcon(update.status)"
              /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ele-card>
</template>

<script setup>
  import { ref, reactive, onMounted, onUnmounted } from 'vue';
  import * as echarts from 'echarts';
  import {
    TrendCharts,
    Upload,
    Edit,
    Delete,
    Share,
    CaretTop,
    CaretBottom,
    CircleCheck,
    Clock,
    Warning
  } from '@element-plus/icons-vue';

  const frequencyChart = ref(null);
  let chartInstance = null;
  const activeTimeFilter = ref('24h');

  // 时间过滤器
  const timeFilters = reactive([
    { label: '24小时', value: '24h' },
    { label: '7天', value: '7d' },
    { label: '30天', value: '30d' }
  ]);

  // 更新统计
  const updateStats = reactive([
    {
      type: 'total',
      icon: Upload,
      value: '312',
      label: '今日更新',
      trend: 15.6
    },
    {
      type: 'active',
      icon: Edit,
      value: '89',
      label: '活跃编辑',
      trend: 8.2
    },
    {
      type: 'published',
      icon: Share,
      value: '156',
      label: '已发布',
      trend: 12.3
    }
  ]);

  // 最新更新列表
  const recentUpdates = reactive([
    {
      title: '营销活动Banner更新',
      type: 'image',
      typeLabel: '图片',
      time: '2分钟前',
      author: '张三',
      status: 'published'
    },
    {
      title: '产品介绍视频上传',
      type: 'video',
      typeLabel: '视频',
      time: '5分钟前',
      author: '李四',
      status: 'processing'
    },
    {
      title: '公司新闻文章发布',
      type: 'article',
      typeLabel: '文章',
      time: '8分钟前',
      author: '王五',
      status: 'published'
    },
    {
      title: '产品手册PDF更新',
      type: 'document',
      typeLabel: '文档',
      time: '12分钟前',
      author: '赵六',
      status: 'draft'
    },
    {
      title: '品牌Logo设计稿',
      type: 'design',
      typeLabel: '设计',
      time: '15分钟前',
      author: '孙七',
      status: 'review'
    }
  ]);

  // 获取状态图标
  const getStatusIcon = (status) => {
    switch (status) {
      case 'published':
        return CircleCheck;
      case 'processing':
        return Clock;
      case 'review':
        return Warning;
      default:
        return Edit;
    }
  };

  // 初始化频率图表
  const initFrequencyChart = () => {
    if (frequencyChart.value) {
      chartInstance = echarts.init(frequencyChart.value);

      const hours = Array.from(
        { length: 24 },
        (_, i) => `${i.toString().padStart(2, '0')}:00`
      );
      const data = hours.map(() => Math.floor(Math.random() * 50) + 10);

      const option = {
        backgroundColor: 'transparent',
        grid: {
          left: '5%',
          right: '5%',
          top: '10%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: hours,
          axisLine: {
            lineStyle: {
              color: 'rgba(74, 172, 254, 0.2)'
            }
          },
          axisLabel: {
            color: 'rgba(255, 255, 255, 0.6)',
            fontSize: 10,
            interval: 3
          },
          axisTick: { show: false }
        },
        yAxis: {
          type: 'value',
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            color: 'rgba(255, 255, 255, 0.6)',
            fontSize: 10
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(74, 172, 254, 0.1)'
            }
          }
        },
        series: [
          {
            type: 'bar',
            data: data,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: '#00d4ff' },
                  { offset: 1, color: '#4facfe' }
                ]
              },
              borderRadius: [2, 2, 0, 0]
            },
            emphasis: {
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: '#00ff88' },
                    { offset: 1, color: '#00d4aa' }
                  ]
                }
              }
            },
            barWidth: '60%'
          }
        ],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 20, 60, 0.9)',
          borderColor: '#00d4ff',
          borderWidth: 1,
          textStyle: { color: '#ffffff' },
          formatter: (params) => {
            const param = params[0];
            return `${param.name}<br/>更新次数: ${param.value}`;
          }
        }
      };

      chartInstance.setOption(option);
    }
  };

  onMounted(() => {
    initFrequencyChart();

    // 窗口大小变化时重新调整图表
    const resizeHandler = () => {
      if (chartInstance) {
        chartInstance.resize();
      }
    };
    window.addEventListener('resize', resizeHandler);
  });

  onUnmounted(() => {
    if (chartInstance) {
      chartInstance.dispose();
    }
    window.removeEventListener('resize', resizeHandler);
  });

  defineOptions({ name: 'ContentUpdateCard' });
</script>

<style scoped>
  .content-update-card {
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(26, 29, 74, 0.9) 0%,
      rgba(45, 53, 97, 0.8) 100%
    );
    border: 1px solid rgba(74, 172, 254, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
  }

  .card-header-custom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .header-icon-wrapper {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #00d4ff, #4facfe);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
  }

  .header-icon {
    font-size: 16px;
    color: white;
  }

  .header-title {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .header-right .update-frequency {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
  }

  .frequency-dot {
    width: 6px;
    height: 6px;
    background: #00ff88;
    border-radius: 50%;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  .update-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: calc(100% - 60px);
  }

  /* 更新统计 */
  .update-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .stat-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(74, 172, 254, 0.1);
    border-radius: 8px;
    padding: 12px;
    position: relative;
    overflow: hidden;
  }

  .stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #00d4ff, #4facfe, #00ff88);
  }

  .stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .stat-icon {
    width: 24px;
    height: 24px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
  }

  .stat-icon.icon-total {
    background: linear-gradient(135deg, #00d4ff, #4facfe);
  }

  .stat-icon.icon-active {
    background: linear-gradient(135deg, #ff9f43, #ff6b6b);
  }

  .stat-icon.icon-published {
    background: linear-gradient(135deg, #00d4aa, #00ff88);
  }

  .stat-trend {
    display: flex;
    align-items: center;
    gap: 3px;
    font-size: 10px;
    font-weight: 500;
  }

  .stat-trend.positive {
    color: #00ff88;
  }

  .stat-trend.negative {
    color: #ff6b6b;
  }

  .stat-body {
    text-align: center;
  }

  .stat-value {
    font-size: 18px;
    font-weight: 700;
    color: #ffffff;
    line-height: 1;
  }

  .stat-label {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 4px;
  }

  /* 频率图表 */
  .frequency-chart-section {
    flex: 1;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(74, 172, 254, 0.1);
    border-radius: 12px;
    padding: 16px;
  }

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .chart-header h4 {
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
  }

  .time-filter :deep(.el-button-group) {
    box-shadow: none;
  }

  .time-filter :deep(.el-button) {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(74, 172, 254, 0.2);
    color: rgba(255, 255, 255, 0.8);
    font-size: 11px;
    padding: 4px 8px;
  }

  .time-filter :deep(.el-button:hover) {
    background: rgba(0, 212, 255, 0.1);
    border-color: rgba(0, 212, 255, 0.3);
    color: #00d4ff;
  }

  .time-filter :deep(.el-button--primary) {
    background: linear-gradient(135deg, #00d4ff, #4facfe);
    border-color: #00d4ff;
    color: white;
  }

  .frequency-chart {
    width: 100%;
    height: 120px;
  }

  /* 最新更新列表 */
  .recent-updates {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(74, 172, 254, 0.1);
    border-radius: 12px;
    padding: 16px;
  }

  .updates-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .updates-header h4 {
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
  }

  .updates-count {
    background: #00d4ff;
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
  }

  .updates-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 200px;
    overflow-y: auto;
  }

  .updates-list::-webkit-scrollbar {
    width: 4px;
  }

  .updates-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  .updates-list::-webkit-scrollbar-thumb {
    background: rgba(0, 212, 255, 0.3);
    border-radius: 2px;
  }

  .updates-list::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 212, 255, 0.5);
  }

  .update-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 8px;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(74, 172, 254, 0.05);
    transition: all 0.3s ease;
  }

  .update-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(74, 172, 254, 0.2);
  }

  .update-indicator {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 4px;
  }

  .indicator-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    position: relative;
    z-index: 1;
  }

  .indicator-dot.dot-image {
    background: #00d4ff;
    box-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
  }

  .indicator-dot.dot-video {
    background: #ff9f43;
    box-shadow: 0 0 8px rgba(255, 159, 67, 0.5);
  }

  .indicator-dot.dot-article {
    background: #00ff88;
    box-shadow: 0 0 8px rgba(0, 255, 136, 0.5);
  }

  .indicator-dot.dot-document {
    background: #ff6b6b;
    box-shadow: 0 0 8px rgba(255, 107, 107, 0.5);
  }

  .indicator-dot.dot-design {
    background: #a855f7;
    box-shadow: 0 0 8px rgba(168, 85, 247, 0.5);
  }

  .indicator-line {
    width: 1px;
    height: 24px;
    background: rgba(74, 172, 254, 0.2);
    margin-top: 4px;
  }

  .update-content-info {
    flex: 1;
  }

  .update-title {
    font-size: 13px;
    font-weight: 500;
    color: #ffffff;
    line-height: 1.3;
    margin-bottom: 4px;
  }

  .update-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 11px;
    color: rgba(255, 255, 255, 0.6);
  }

  .update-type {
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
  }

  .update-type.type-image {
    background: rgba(0, 212, 255, 0.2);
    color: #00d4ff;
  }

  .update-type.type-video {
    background: rgba(255, 159, 67, 0.2);
    color: #ff9f43;
  }

  .update-type.type-article {
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
  }

  .update-type.type-document {
    background: rgba(255, 107, 107, 0.2);
    color: #ff6b6b;
  }

  .update-type.type-design {
    background: rgba(168, 85, 247, 0.2);
    color: #a855f7;
  }

  .update-status {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    font-size: 12px;
  }

  .update-status.status-published {
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
  }

  .update-status.status-processing {
    background: rgba(255, 159, 67, 0.2);
    color: #ff9f43;
  }

  .update-status.status-review {
    background: rgba(255, 159, 67, 0.2);
    color: #ff9f43;
  }

  .update-status.status-draft {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .update-stats {
      grid-template-columns: 1fr;
      gap: 10px;
    }

    .stat-card {
      padding: 10px;
    }

    .frequency-chart {
      height: 100px;
    }

    .updates-list {
      max-height: 150px;
    }
  }
</style>
