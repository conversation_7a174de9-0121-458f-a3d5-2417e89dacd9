<!-- 信息发布统计卡片 -->
<template>
  <div class="statistics-container">
    <div class="stat-card primary">
      <div class="stat-background">
        <div class="stat-icon-bg"></div>
      </div>
      <div class="stat-header">
        <div class="stat-icon">
          <el-icon><Monitor /></el-icon>
        </div>
        <div class="stat-trend positive">
          <el-icon><TrendCharts /></el-icon>
          <span>+12.5%</span>
        </div>
      </div>
      <div class="stat-content">
        <div class="stat-value">2,847</div>
        <div class="stat-label">在线发布设备</div>
      </div>
    </div>

    <div class="stat-card warning">
      <div class="stat-background">
        <div class="stat-icon-bg"></div>
      </div>
      <div class="stat-header">
        <div class="stat-icon">
          <el-icon><Promotion /></el-icon>
        </div>
        <div class="stat-trend positive">
          <el-icon><TrendCharts /></el-icon>
          <span>+8.3%</span>
        </div>
      </div>
      <div class="stat-content">
        <div class="stat-value">156.8k</div>
        <div class="stat-label">今日信息投放量</div>
      </div>
    </div>

    <div class="stat-card info">
      <div class="stat-background">
        <div class="stat-icon-bg"></div>
      </div>
      <div class="stat-header">
        <div class="stat-icon">
          <el-icon><Timer /></el-icon>
        </div>
        <div class="stat-trend positive">
          <el-icon><TrendCharts /></el-icon>
          <span>+15.7%</span>
        </div>
      </div>
      <div class="stat-content">
        <div class="stat-value">847.2h</div>
        <div class="stat-label">累计播放时长</div>
      </div>
    </div>

    <div class="stat-card success">
      <div class="stat-background">
        <div class="stat-icon-bg"></div>
      </div>
      <div class="stat-header">
        <div class="stat-icon">
          <el-icon><User /></el-icon>
        </div>
        <div class="stat-trend positive">
          <el-icon><TrendCharts /></el-icon>
          <span>+22.1%</span>
        </div>
      </div>
      <div class="stat-content">
        <div class="stat-value">89.3w</div>
        <div class="stat-label">信息覆盖人群</div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import {
    Monitor,
    Promotion,
    Timer,
    User,
    TrendCharts
  } from '@element-plus/icons-vue';

  defineOptions({ name: 'StatisticsCard' });
</script>

<style scoped>
  .statistics-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    margin: 0;
  }

  .stat-card {
    position: relative;
    padding: 20px 18px;
    border-radius: 12px;
    background: linear-gradient(
      135deg,
      var(--el-bg-color-page) 0%,
      var(--el-fill-color-lighter) 100%
    );
    border: 1px solid var(--el-border-color-lighter);
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 2px 8px var(--el-box-shadow-lighter);
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px var(--el-box-shadow-light);
    border-color: var(--el-color-primary-light-7);
  }

  .stat-background {
    position: absolute;
    top: -15px;
    right: -15px;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    opacity: 0.06;
  }

  .stat-card.primary .stat-background {
    background: var(--el-color-primary);
  }

  .stat-card.warning .stat-background {
    background: var(--el-color-warning);
  }

  .stat-card.info .stat-background {
    background: var(--el-color-info);
  }

  .stat-card.success .stat-background {
    background: var(--el-color-success);
  }

  .stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
    position: relative;
    z-index: 1;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
  }

  .stat-card.primary .stat-icon {
    background: linear-gradient(
      135deg,
      var(--el-color-primary),
      var(--el-color-primary-dark-2)
    );
  }

  .stat-card.warning .stat-icon {
    background: linear-gradient(
      135deg,
      var(--el-color-warning),
      var(--el-color-warning-dark-2)
    );
  }

  .stat-card.info .stat-icon {
    background: linear-gradient(
      135deg,
      var(--el-color-info),
      var(--el-color-info-dark-2)
    );
  }

  .stat-card.success .stat-icon {
    background: linear-gradient(
      135deg,
      var(--el-color-success),
      var(--el-color-success-dark-2)
    );
  }

  .stat-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 16px;
    font-size: 11px;
    font-weight: 600;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .stat-trend.positive {
    background: linear-gradient(
      135deg,
      var(--el-color-success-light-8),
      var(--el-color-success-light-9)
    );
    color: var(--el-color-success-dark-2);
    border: 1px solid var(--el-color-success-light-7);
  }

  .stat-trend.negative {
    background: linear-gradient(
      135deg,
      var(--el-color-danger-light-8),
      var(--el-color-danger-light-9)
    );
    color: var(--el-color-danger-dark-2);
    border: 1px solid var(--el-color-danger-light-7);
  }

  .stat-content {
    position: relative;
    z-index: 1;
  }

  .stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--el-text-color-primary);
    line-height: 1.1;
    margin-bottom: 6px;
    background: linear-gradient(
      135deg,
      var(--el-text-color-primary),
      var(--el-text-color-regular)
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .stat-label {
    font-size: 12px;
    color: var(--el-text-color-regular);
    font-weight: 500;
    letter-spacing: 0.3px;
    line-height: 1.2;
  }

  /* 响应式设计 */
  @media (max-width: 1400px) {
    .statistics-container {
      gap: 18px;
    }

    .stat-card {
      padding: 18px 16px;
    }

    .stat-value {
      font-size: 22px;
    }

    .stat-icon {
      width: 36px;
      height: 36px;
      font-size: 16px;
    }
  }

  @media (max-width: 1200px) {
    .statistics-container {
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }

    .stat-card {
      padding: 16px 14px;
    }

    .stat-icon {
      width: 32px;
      height: 32px;
      font-size: 14px;
    }

    .stat-value {
      font-size: 20px;
    }

    .stat-label {
      font-size: 11px;
    }
  }

  @media (max-width: 768px) {
    .statistics-container {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .stat-card {
      padding: 14px 12px;
    }

    .stat-icon {
      width: 28px;
      height: 28px;
      font-size: 12px;
    }

    .stat-value {
      font-size: 18px;
    }

    .stat-label {
      font-size: 10px;
    }

    .stat-trend {
      font-size: 10px;
      padding: 3px 6px;
    }
  }
</style>
