<template>
  <div class="dashboard-container">
    <!-- 统计卡片区域 -->
    <div class="statistics-section">
      <statistics-card />
    </div>

    <!-- 主要内容区域 -->
    <div class="main-grid">
      <!-- 左侧：区域设备分布 -->
      <div class="map-section">
        <map-card />
      </div>

      <!-- 右侧：发布监控中心 -->
      <div class="monitor-section">
        <online-num />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import StatisticsCard from './components/statistics-card.vue';
  import MapCard from './components/map-card.vue';
  import OnlineNum from './components/online-num.vue';

  defineOptions({ name: 'DashboardMonitor' });
</script>

<style scoped>
  .dashboard-container {
    min-height: 100vh;
    background: var(--el-bg-color-page);
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    box-sizing: border-box;
    position: relative;
  }

  /* 统计卡片区域 */
  .statistics-section {
    width: 100%;
  }

  /* 主要内容网格 */
  .main-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    align-items: stretch;
  }

  .map-section,
  .monitor-section {
    width: 100%;
    height: 100%;
  }

  /* 响应式设计 */
  @media (max-width: 1400px) {
    .dashboard-container {
      padding: 20px;
      gap: 20px;
    }

    .main-grid {
      gap: 20px;
    }
  }

  @media (max-width: 1200px) {
    .main-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    .analysis-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    .analysis-sidebar {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }
  }

  @media (max-width: 768px) {
    .dashboard-container {
      padding: 16px;
      gap: 16px;
    }

    .main-grid {
      gap: 16px;
    }
  }

  @media (max-width: 480px) {
    .dashboard-container {
      padding: 12px;
      gap: 12px;
    }

    .main-grid {
      gap: 12px;
    }
  }

  /* 确保所有卡片高度一致 */
  :deep(.ele-card) {
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(74, 172, 254, 0.2);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(0, 212, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
  }

  :deep(.ele-card:hover) {
    border-color: rgba(0, 212, 255, 0.4);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.4),
      0 0 30px rgba(0, 212, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
  }

  :deep(.ele-card .el-card__body) {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
  }

  :deep(.ele-card .el-card__header) {
    background: rgba(255, 255, 255, 0.02);
    border-bottom: 1px solid rgba(74, 172, 254, 0.1);
    padding: 16px 20px;
  }

  /* 统一卡片间距 */
  :deep(.statistics-container) {
    margin: 0;
  }

  /* 确保地图卡片合适的高度 */
  .map-section :deep(.device-map-card) {
    min-height: 100%;
  }

  /* 简化的悬停效果 */
  :deep(.card-header-custom .header-icon-wrapper) {
    transition: box-shadow 0.3s ease;
  }

  :deep(.card-header-custom .header-icon-wrapper:hover) {
    box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
  }
</style>
