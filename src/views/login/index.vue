<template>
  <div class="login-bg">
    <div class="login-panel">
      <!-- 左侧品牌区 -->
      <div class="login-left">
        <div class="login-logo-row">
          <img class="login-logo" src="@/assets/logo.png" alt="logo" />
        </div>
        <SloganIcon :alt="appName" class="animate-float h-64 w-2/5" />
        <div class="login-desc-main">开箱即用的大型中后台管理系统</div>
        <div class="login-desc-sub">提升企业效率，打造卓越的业务体验与价值</div>
      </div>
      <!-- 右侧表单区 -->
      <div class="login-right">
        <div class="login-form-card">
          <div class="login-form-title">
            欢迎回来 <span class="wave">👋</span>
          </div>
          <div class="login-form-subtitle"> 请输入您的账户信息 </div>
          <el-form
            ref="formLogin"
            size="large"
            :model="loginData.loginForm"
            :rules="LoginRules"
            @submit.prevent=""
          >
            <el-form-item
              prop="tenantName"
              v-if="loginData.tenantEnable === 'true'"
            >
              <el-input
                clearable
                v-model="loginData.loginForm.tenantName"
                placeholder="请输入租户名称"
                :prefix-icon="House"
              />
            </el-form-item>
            <el-form-item prop="username">
              <el-input
                clearable
                v-model="loginData.loginForm.username"
                placeholder="请输入登录账号"
                :prefix-icon="User"
              />
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                show-password
                v-model="loginData.loginForm.password"
                placeholder="请输入登录密码"
                :prefix-icon="Lock"
              />
            </el-form-item>
            <!-- 验证码组件可按需放置 -->
            <el-form-item>
              <el-checkbox v-model="loginData.loginForm.rememberMe"
                >记住账号</el-checkbox
              >
            </el-form-item>
            <el-form-item>
              <el-button
                size="large"
                type="primary"
                :loading="loginLoading"
                style="width: 100%"
                @click="getCode()"
              >
                登录
              </el-button>
            </el-form-item>
          </el-form>
          <Verify
            ref="verify"
            :captchaType="captchaType"
            :imgSize="{ width: '400px', height: '200px' }"
            mode="pop"
            @success="handleLogin"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, unref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { House, User, Lock } from '@element-plus/icons-vue';
  import { usePageTab } from '@/utils/use-page-tab';
  import { login, getTenantIdByName } from '@/api/login';
  import * as authUtil from '@/utils/auth';
  import { Verify } from '@/components/Verifition';

  import { useFormValid } from './useLogin';
  import SloganIcon from './slogan.vue';

  const { currentRoute } = useRouter();
  const { goHomeRoute, cleanPageTabs } = usePageTab();
  const formLogin = ref();

  const loginData = reactive({
    isShowPassword: false,
    captchaEnable: import.meta.env.VITE_APP_CAPTCHA_ENABLE,
    tenantEnable: import.meta.env.VITE_APP_TENANT_ENABLE,
    loginForm: {
      tenantName: import.meta.env.VITE_APP_DEFAULT_LOGIN_TENANT || '',
      username: '',
      password: '',
      captchaVerification: '',
      rememberMe: true
    }
  });

  const LoginRules = reactive({
    tenantName: [
      {
        required: true,
        message: '请输入租户名称',
        type: 'string',
        trigger: 'blur'
      }
    ],
    username: [
      {
        required: true,
        message: '请输入登录账号',
        type: 'string',
        trigger: 'blur'
      }
    ],
    password: [
      {
        required: true,
        message: '请输入登录密码',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  const verify = ref();
  const captchaType = ref('blockPuzzle'); // blockPuzzle 滑块 clickWord 点击文字
  // 获取验证码
  const getCode = async () => {
    // 情况一，未开启：则直接登录
    if (loginData.captchaEnable === 'false') {
      await handleLogin({});
    } else {
      // 情况二，已开启：则展示验证码；只有完成验证码的情况，才进行登录
      const data = await validForm();
      if (!data) {
        return;
      }
      // 弹出验证码
      verify.value.show();
    }
  };
  // 获取租户 ID
  const getTenantId = async () => {
    if (loginData.tenantEnable === 'true') {
      const res = await getTenantIdByName(loginData.loginForm.tenantName);
      authUtil.setTenantId(res);
    }
  };
  // 记住我
  const getLoginFormCache = () => {
    const loginForm = authUtil.getLoginForm();
    if (loginForm) {
      loginData.loginForm = {
        ...loginData.loginForm,
        username: loginForm.username
          ? loginForm.username
          : loginData.loginForm.username,
        password: loginForm.password
          ? loginForm.password
          : loginData.loginForm.password,
        rememberMe: loginForm.rememberMe,
        tenantName: loginForm.tenantName
          ? loginForm.tenantName
          : loginData.loginForm.tenantName
      };
    }
  };
  const loginLoading = ref(false);
  const { validForm } = useFormValid(formLogin);
  // 登录
  const handleLogin = async (params) => {
    loginLoading.value = true;
    try {
      await getTenantId();
      const data = await validForm();
      if (!data) {
        return;
      }
      const loginDataLoginForm = { ...loginData.loginForm };
      loginDataLoginForm.captchaVerification = params.captchaVerification;
      const res = await login(loginDataLoginForm);
      if (!res) {
        return;
      }
      if (loginDataLoginForm.rememberMe) {
        authUtil.setLoginForm(loginDataLoginForm);
      } else {
        authUtil.removeLoginForm();
      }
      authUtil.setToken(res);
      cleanPageTabs();
      goHome();
    } finally {
      loginLoading.value = false;
    }
  };
  const goHome = () => {
    const { query } = unref(currentRoute);
    goHomeRoute(query.from);
  };
  if (authUtil.getAccessToken()) {
    goHome();
  }
  onMounted(() => {
    getLoginFormCache();
  });
</script>

<style lang="scss" scoped>
  .login-bg {
    min-height: 100vh;
    width: 100vw;
    background: linear-gradient(120deg, #f8fafc 0%, #e0e7ff 100%);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .login-panel {
    width: 1200px;
    max-width: 98vw;
    height: 650px;
    background: #fff;
    border-radius: 18px;
    box-shadow: 0 8px 40px 0 rgba(0, 0, 0, 0.08);
    display: flex;
    overflow: hidden;
    position: relative;
  }
  .login-left {
    flex: 1.5;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    background: linear-gradient(120deg, #f8fafc 0%, #e0e7ff 100%);
    position: relative;
    padding: 48px 0 0 0;
  }
  .login-logo-row {
    display: flex;
    align-items: center;
    width: 100%;
    padding-left: 48px;
  }
  .login-logo {
    width: 158px;
    height: 118px;
    margin-right: 12px;
    border-radius: 12px;
    object-fit: contain;
  }
  .login-logo-title {
    font-size: 22px;
    font-weight: 700;
    color: #22223b;
    letter-spacing: 1.5px;
    font-family:
      'AliPuHui', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  }
  .login-illustration {
    width: 340px;
    max-width: 90%;
    margin: 0 auto 32px auto;
    display: block;
    border-radius: 18px;
    box-shadow: 0 4px 32px 0 rgba(0, 0, 0, 0.08);
    background: #fff;
    object-fit: cover;
  }
  .login-desc-main {
    font-size: 24px;
    font-weight: 600;
    color: #22223b;
    text-align: center;
    margin-bottom: 12px;
    margin-top: 12px;
    letter-spacing: 1.2px;
  }
  .login-desc-sub {
    font-size: 16px;
    color: #6b7280;
    text-align: center;
    font-weight: 400;
    letter-spacing: 1px;
  }
  .login-right {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    min-width: 0;
  }
  .login-form-card {
    width: 380px;
    background: #fff;
    border-radius: 16px;
    padding: 48px 36px 32px 36px;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    position: relative;
  }
  .login-form-title {
    font-size: 28px;
    font-weight: 700;
    color: #22223b;
    margin-bottom: 8px;
    letter-spacing: 1.2px;
    text-align: left;
  }
  .wave {
    font-size: 26px;
    vertical-align: middle;
    margin-left: 2px;
  }
  .login-form-subtitle {
    font-size: 15px;
    color: #6b7280;
    margin-bottom: 28px;
    letter-spacing: 1px;
    text-align: left;
  }
  .login-link {
    color: #2563eb;
    text-decoration: none;
    font-size: 14px;
    margin-left: 8px;
    &:hover {
      text-decoration: underline;
    }
  }
  .login-other {
    display: flex;
    justify-content: space-between;
    margin: 18px 0 0 0;
  }
  .login-other-btn {
    width: 48%;
    font-size: 14px;
  }
  .login-social {
    display: flex;
    justify-content: center;
    margin: 24px 0 0 0;
    gap: 18px;
    font-size: 22px;
    color: #bdbdbd;
    .iconfont {
      cursor: pointer;
      transition: color 0.2s;
      &:hover {
        color: #2563eb;
      }
    }
  }
  .login-bottom {
    margin-top: 24px;
    text-align: center;
    color: #6b7280;
    font-size: 14px;
  }
  :deep(.el-form-item) {
    margin-bottom: 22px;
  }
  :deep(.el-input__prefix-inner > .el-icon) {
    margin-right: 10px;
    transform: scale(1.12);
  }
  :deep(.el-checkbox__label) {
    color: #22223b;
  }
  @media (max-width: 1100px) {
    .login-panel {
      flex-direction: column;
      height: auto;
      min-height: 100vh;
      border-radius: 0;
      box-shadow: none;
    }
    .login-left {
      min-height: 220px;
      padding: 32px 0 0 0;
      align-items: center;
      justify-content: flex-start;
    }
    .login-logo-row {
      padding-left: 0;
      justify-content: center;
    }
    .login-illustration {
      width: 180px;
      margin-bottom: 18px;
      border-radius: 12px;
    }
    .login-desc-main {
      font-size: 18px;
    }
    .login-desc-sub {
      font-size: 13px;
    }
    .login-form-card {
      width: 96vw;
      max-width: 400px;
      border-radius: 12px;
      box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 0.06);
      padding: 32px 16px 24px 16px;
    }
  }
  @media (max-width: 600px) {
    .login-form-card {
      width: 100vw;
      max-width: 100vw;
      border-radius: 0;
      box-shadow: none;
      padding: 24px 8px 16px 8px;
    }
    .login-illustration {
      width: 100px;
    }
    .login-form-title {
      font-size: 18px;
    }
    .login-desc-main {
      font-size: 14px;
    }
  }
  @font-face {
    font-family: 'AliPuHui';
    font-weight: 300;
    src:
      url('//at.alicdn.com/wf/webfont/jWZHcEP2lzge/5AfKUTWZEo8W.woff2')
        format('woff2'),
      url('//at.alicdn.com/wf/webfont/jWZHcEP2lzge/Dvhs41TtRdYF.woff')
        format('woff');
    font-display: swap;
  }
  @keyframes floatY {
    0% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-24px);
    }
    100% {
      transform: translateY(0);
    }
  }

  .animate-float {
    animation: floatY 3s ease-in-out infinite;
  }
</style>
