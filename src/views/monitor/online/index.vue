<template>
  <ele-page>
    <!-- 搜索表单 -->
    <online-search ref="searchRef" @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="tokenId"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        :loading="loading"
        highlight-current-row
        :export-config="{
          fileName: '在线用户',
          datasource: async () => datasource
        }"
        :print-config="{ datasource: async () => datasource }"
        cache-key="monitorOnlineTable"
        @refresh="reload(getWhere())"
      >
        <template #action="{ row }">
          <el-link type="danger" underline="never" @click="kickout(row)">
            强退
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import dayjs from 'dayjs';
  import { ElMessageBox } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus';
  import { usePermission } from '@/utils/use-permission';
  import OnlineSearch from './components/online-search.vue';
  import { pageOnlines, kickoutOnline } from '@/api/monitor/online';

  defineOptions({ name: 'MonitorOnline' });

  const { hasPermission } = usePermission();

  const searchRef = ref(null);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = computed(() => {
    const cols = [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center' /* ,
        fixed: 'left' */
      },
      {
        prop: 'tokenId',
        label: '会话编号',
        align: 'center',
        minWidth: 160
      },
      {
        prop: 'userName',
        label: '登录名称',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'deptName',
        label: '部门名称',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'ipaddr',
        label: '登录地址',
        align: 'center',
        minWidth: 140
      },
      {
        prop: 'loginLocation',
        label: '登录地点',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'browser',
        label: '浏览器',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'os',
        label: '操作系统',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'loginTime',
        label: '登录时间',
        align: 'center',
        width: 180,
        formatter: (row) => dayjs(row.loginTime).format('YYYY-MM-DD HH:mm:ss')
      }
    ];
    if (hasPermission('monitor:online:forceLogout')) {
      cols.push({
        columnKey: 'action',
        label: '操作',
        width: 80,
        align: 'center',
        slot: 'action' /* ,
        fixed: 'right' */,
        hideInPrint: true,
        hideInExport: true
      });
    }
    return cols;
  });

  /** 表格数据源 */
  const datasource = ref([]);

  /** 请求状态 */
  const loading = ref(false);

  /** 搜索 */
  const reload = (where) => {
    loading.value = true;
    pageOnlines(where)
      .then((res) => {
        loading.value = false;
        datasource.value = res.rows;
        tableRef.value?.reload?.({ page: 1 });
      })
      .catch((e) => {
        loading.value = false;
        console.error(e);
      });
  };

  /** 强退 */
  const kickout = (row) => {
    ElMessageBox.confirm(
      '是否确认强退名称为“' + row.userName + '”的用户？',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        kickoutOnline(row.tokenId)
          .then(() => {
            loading.close();
            EleMessage.success({ message: '强退成功', plain: true });
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error({ message: e.message, plain: true });
          });
      })
      .catch(() => {});
  };

  /** 获取当前搜索条件 */
  const getWhere = () => {
    return searchRef.value?.getWhere?.();
  };

  reload();
</script>
