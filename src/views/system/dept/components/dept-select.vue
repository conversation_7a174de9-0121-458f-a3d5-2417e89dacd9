<!-- 部门选择下拉框 -->
<template>
  <el-tree-select
    clearable
    check-strictly
    default-expand-all
    :data="data"
    node-key="id"
    :props="{ label: 'name' }"
    :model-value="modelValue"
    :placeholder="placeholder"
    class="ele-fluid"
    @update:modelValue="updateValue"
  />
</template>

<script setup>
  import { ref } from 'vue';
  import { toTree } from 'ele-admin-plus/es';
  import { getDeptPage } from '@/api/system/dept';

  const emit = defineEmits(['update:modelValue']);

  defineProps({
    /** 选中的部门 */
    modelValue: [Number, String],
    /** 提示信息 */
    placeholder: {
      type: String,
      default: '请选择归属部门'
    }
  });

  /** 部门数据 */
  const data = ref([]);

  /** 更新选中数据 */
  const updateValue = (value) => {
    emit('update:modelValue', value);
  };
  onMounted(() => {
    getDeptPage().then((list) => {
      data.value = toTree({
        data: list,
        idField: 'id',
        parentIdField: 'parentId'
      });
    });
  });
</script>
