<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <role-search @search="reload" />
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="systemRoleTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            class="ele-btn-icon"
            :icon="DownloadOutlined"
            @click="exportData"
          >
            导出
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.COMMON_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <template v-if="row.id !== 1">
            <el-link
              type="primary"
              v-permission="['system:role:update']"
              :underline="false"
              @click="openEdit(row)"
            >
              修改
            </el-link>
            <el-divider
              direction="vertical"
              v-permission="['system:role:delete']"
            />
            <el-link
              type="danger"
              v-permission="['system:role:delete']"
              :underline="false"
              @click="remove(row)"
            >
              删除
            </el-link>
            <el-divider direction="vertical" />
            <ele-dropdown
              :items="[
                { title: '菜单权限', command: 'menu' },
                { title: '数据权限', command: 'auth' }
              ]"
              style="display: inline"
              @command="(key) => dropClick(key, row)"
            >
              <el-link type="primary" :underline="false">
                <span>更多</span>
                <el-icon
                  :size="12"
                  style="vertical-align: -1px; margin-left: 2px"
                >
                  <ArrowDown />
                </el-icon>
              </el-link>
            </ele-dropdown>
          </template>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 新增编辑弹窗 -->
    <role-edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 分配数据权限弹窗 -->
    <role-auth v-model="showAuth" :data="current" @done="reload" />
    <!-- 分配菜单权限弹窗 -->
    <role-menu v-model="showMenu" :data="current" @done="reload" />
    <!-- 分配用户弹窗 -->
    <role-user v-model="showUser" :data="current" />
  </ele-page>
</template>
<script setup>
  import { ref, computed } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DICT_TYPE } from '@/utils/dict';
  import {
    PlusOutlined,
    DownloadOutlined,
    ArrowDown
  } from '@/components/icons';
  import RoleSearch from './components/role-search.vue';
  import RoleEdit from './components/role-edit.vue';
  import RoleAuth from './components/role-auth.vue';
  import RoleUser from './components/role-user.vue';
  import RoleMenu from './components/role-menu.vue';
  import { dateFormatter } from '@/utils/formatTime';
  import { getRolePage, deleteRole, exportRole } from '@/api/system/role';

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'selection',
        columnKey: 'selection',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'name',
        label: '角色名称',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'code',
        label: '权限字符',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'sort',
        label: '显示顺序',
        align: 'center',
        minWidth: 50
      },
      {
        prop: 'remark',
        label: '备注',
        width: 140,
        align: 'center'
      },
      {
        prop: 'status',
        label: '状态',
        width: 90,
        align: 'center',
        slot: 'status'
      },
      {
        prop: 'createTime',
        label: '创建时间',
        align: 'center',
        minWidth: 110,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 180,
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 是否显示分配数据权限弹窗 */
  const showAuth = ref(false);

  /** 是否显示分配菜单弹窗 */
  const showMenu = ref(false);

  /** 是否显示分配用户弹窗 */
  const showUser = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return getRolePage({ ...where, ...filters, ...pages });
  };

  /** 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 批量删除 */
  const remove = (row) => {
    ElMessageBox.confirm(`是否确认删除当前角色?`, '系统提示', {
      type: 'warning',
      draggable: true
    }).then(() => {
      const ids = [row.id];
      const loading = EleMessage.loading('请求中..');
      deleteRole(ids)
        .then(() => {
          loading.close();
          EleMessage.success('删除成功');
          reload();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };

  /** 导出数据 */
  const exportData = () => {
    const loading = EleMessage.loading('请求中..');
    tableRef.value?.fetch?.(({ where }) => {
      exportRole(where)
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };

  /** 下拉菜单点击事件 */
  const dropClick = (key, row) => {
    if (key === 'auth') {
      current.value = row ?? null;
      showAuth.value = true;
    } else if (key === 'user') {
      current.value = row ?? null;
      showUser.value = true;
    } else if (key === 'menu') {
      current.value = row ?? null;
      showMenu.value = true;
    }
  };

  /** 修改角色状态 */
  // const editStatus = (checked, row) => {
  //   const status = checked ? '0' : '1';
  //   updateRoleStatus(row.id, status)
  //     .then((msg) => {
  //       row.status = status;
  //       EleMessage.success(msg);
  //     })
  //     .catch((e) => {
  //       EleMessage.error(e.message);
  //     });
  // };
</script>

<script>
  export default {
    name: 'SystemRole'
  };
</script>
