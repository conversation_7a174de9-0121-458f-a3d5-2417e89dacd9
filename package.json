{"name": "sirius-front-pro", "version": "1.4.0", "type": "module", "private": true, "scripts": {"dev": "vite --host", "serve": "vite build && vite preview --host", "build": "vite build", "serve:staging": "vite build --mode staging && vite preview --host", "build:staging": "vite build --mode staging", "lint:eslint": "eslint --cache --max-warnings 0  \"src/**/*.{vue,ts,js}\" --fix", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite/", "clean:lib": "rimraf node_modules"}, "dependencies": {"@amap/amap-jsapi-loader": "1.0.1", "@ant-design/colors": "7.2.1", "@bytemd/plugin-gfm": "1.22.0", "@bytemd/plugin-highlight": "1.22.0", "@element-plus/icons-vue": "2.3.1", "@form-create/designer": "^3.2.6", "@form-create/element-ui": "^3.2.11", "@types/three": "^0.178.1", "@vueuse/core": "13.3.0", "axios": "1.9.0", "bpmn-js": "8.9.0", "bpmn-js-properties-panel": "0.46.0", "bpmn-js-token-simulation": "^0.10.0", "bytemd": "1.22.0", "camunda-bpmn-moddle": "^7.0.1", "countup.js": "2.8.2", "cropperjs": "1.6.2", "crypto-js": "^4.2.0", "dayjs": "1.11.13", "diagram-js": "^12.8.0", "echarts": "5.6.0", "echarts-wordcloud": "2.1.0", "ele-admin-plus": "1.4.0", "element-plus": "2.9.10", "exceljs": "4.4.0", "github-markdown-css": "5.8.1", "highlight.js": "11.11.1", "jsbarcode": "3.11.6", "jsencrypt": "^3.3.2", "jszip": "3.10.1", "lodash-es": "4.17.21", "min-dash": "^4.1.1", "monaco-editor": "0.47.0", "nprogress": "0.2.0", "pinia": "3.0.2", "qs": "^6.12.0", "sortablejs": "1.15.6", "three": "^0.178.0", "tinymce": "5.10.9", "unocss": "^0.58.5", "unplugin-auto-import": "^0.16.7", "vue": "3.5.15", "vue-dompurify-html": "^4.1.4", "vue-echarts": "7.0.3", "vue-i18n": "11.1.5", "vue-router": "4.5.1", "vue-types": "^5.1.1", "vuedraggable": "4.1.0", "web-storage-cache": "^1.1.1", "xgplayer": "3.0.22", "xgplayer-hls": "3.0.22", "xgplayer-music": "3.0.22"}, "devDependencies": {"@types/node": "22.15.23", "@types/nprogress": "0.2.3", "@types/sortablejs": "1.15.8", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "@vitejs/plugin-vue": "5.2.4", "@vue/compiler-sfc": "3.5.15", "eslint": "8.57.1", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-vue": "9.32.0", "postcss": "8.5.3", "prettier": "3.5.3", "rimraf": "6.0.1", "sass": "1.89.0", "typescript": "5.8.3", "unplugin-vue-components": "28.7.0", "vite": "6.3.5", "vite-plugin-compression": "0.5.1", "vue-eslint-parser": "9.4.3", "vue-tsc": "2.2.10"}}