<p align="center">
  <img src="https://cdn.eleadmin.com/20200610/20230820a4.png"/>
</p>

<h1 align="center">RuoYi EleAdmin</h1>

<p align="center">RuoYi 框架前端部分使用 Vue3、ElementPlus、EleAdminPlus 的实现</p>

<p align="center">
	<a href="https://gitee.com/y_project/RuoYi-Vue">
    <img src="https://img.shields.io/badge/RuoYi-v3.9.0-brightgreen"/>
  </a>
	<a href="https://plus.eleadmin.com">
    <img src="https://img.shields.io/badge/EleAdminPlus-v1.4.0-blue"/>
  </a>
</p>

- 🔥 使用 vue3、pinia、element-plus、ele-admin-plus 等主流技术栈
- 💪 组件丰富、代码规范，使用 Composition API、script setup 语法
- 🌈 界面美观，对所有主题变量、组件样式都进行重新设计、精心打磨

## 🌍在线体验

- 演示地址：https://ruoyi.eleadmin.com
- 登录账号：admin
- 登录密码：admin123

## 🔨安装运行

```bash
# 安装依赖
pnpm install --registry=https://registry.npmmirror.com

# 运行项目
npm run dev
```

运行成功后在浏览器地址栏输入 `http://localhost:5173` 访问。

默认使用 RuoYi 线上演示接口，可下载 [RuoYi 后端](https://gitee.com/y_project/RuoYi-Vue) 并运行后，在 `.env.development` 文件中将接口地址改为本地后端接口：

```bash
# 开发环境接口地址
#VITE_API_URL=https://vue.ruoyi.vip/prod-api
VITE_API_URL=http://localhost:8080
```

## 💻系统截图

<table align="center" cellspacing="0" cellpadding="0">
  <tbody>
    <tr>
      <td align="center" valign="middle">
        <img width="100%" src="https://cdn.eleadmin.com/20200610/2023082001.jpeg"/>
      </td>
      <td align="center" valign="middle">
        <img width="100%" src="https://cdn.eleadmin.com/20200610/2023082002.jpeg"/>
      </td>
    </tr>
    <tr>
      <td align="center" valign="middle">
        <img width="100%" src="https://cdn.eleadmin.com/20200610/2023082003.jpeg"/>
      </td>
      <td align="center" valign="middle">
        <img width="100%" src="https://cdn.eleadmin.com/20200610/2023082004.jpeg"/>
      </td>
    </tr>
    <tr>
      <td align="center" valign="middle">
        <img width="100%" src="https://cdn.eleadmin.com/20200610/2023082005.jpeg"/>
      </td>
      <td align="center" valign="middle">
        <img width="100%" src="https://cdn.eleadmin.com/20200610/2023082006.jpeg"/>
      </td>
    </tr>
    <tr>
      <td align="center" valign="middle">
        <img width="100%" src="https://cdn.eleadmin.com/20200610/2023082007.jpeg"/>
      </td>
      <td align="center" valign="middle">
        <img width="100%" src="https://cdn.eleadmin.com/20200610/2023082008.jpeg"/>
      </td>
    </tr>
    <tr>
      <td align="center" valign="middle">
        <img width="100%" src="https://cdn.eleadmin.com/20200610/2023082009.jpeg"/>
      </td>
      <td align="center" valign="middle">
        <img width="100%" src="https://cdn.eleadmin.com/20200610/2023082010.jpeg"/>
      </td>
    </tr>
    <tr>
      <td align="center" valign="middle">
        <img width="100%" src="https://cdn.eleadmin.com/20200610/2023082011.jpeg"/>
      </td>
      <td align="center" valign="middle">
        <img width="100%" src="https://cdn.eleadmin.com/20200610/2023082012.jpeg"/>
      </td>
    </tr>
    <tr>
      <td align="center" valign="middle">
        <img width="100%" src="https://cdn.eleadmin.com/20200610/2023082013.jpeg"/>
      </td>
      <td align="center" valign="middle">
        <img width="100%" src="https://cdn.eleadmin.com/20200610/2023082014.jpeg"/>
      </td>
    </tr>
    <tr>
      <td align="center" valign="middle">
        <img width="100%" src="https://cdn.eleadmin.com/20200610/2023082015.jpeg"/>
      </td>
      <td align="center" valign="middle">
        <img width="100%" src="https://cdn.eleadmin.com/20200610/2023082016.jpeg"/>
      </td>
    </tr>
    </tbody>
</table>

## 📖开发文档

- RuoYi 分离版开发文档：[http://doc.ruoyi.vip/ruoyi-vue/](http://doc.ruoyi.vip/ruoyi-vue/)
- EleAdminPlus 开发文档：[https://eleadmin.com/doc/eleadminplus/](https://eleadmin.com/doc/eleadminplus/)
